{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\127a50fba098f59f5efe01000db9ca9e\\transformed\\material-1.10.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1386,1476,1572,1662,1755,1862,1967,2086,2211,2332,2545,2804,3075,3293,3525,3761,4011,4224,4433,4664,4865,4981,5151,5472,6501,6958,7462,7970,8479,8993,9498,10002,10507,11013,11515,12021,12530,13038,13537,14044,14552,14844,15138,15438,15738,16067,16408,16546,16690,16846,17239,17457,17679,17905,18121,18231,18401,18591,18832,19091", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1381,1471,1567,1657,1750,1857,1962,2081,2206,2327,2540,2799,3070,3288,3520,3756,4006,4219,4428,4659,4860,4976,5146,5467,6496,6953,7457,7965,8474,8988,9493,9997,10502,11008,11510,12016,12525,13033,13532,14039,14547,14839,15133,15433,15733,16062,16403,16541,16685,16841,17234,17452,17674,17900,18116,18226,18396,18586,18827,19086,19263"}, "to": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,22,23,24,25,86,87,88,89,91,92,93,96,99,194,197,200,203,209,212,215,282,285,286,289,299,310,371,380,389,398,407,416,425,434,443,452,461,470,479,488,497,506,512,518,524,577,581,585,586,587,588,592,595,598,601,620,621,624,627,631,635", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "232,330,426,522,620,688,767,855,943,1031,1119,1206,1293,1380,1726,1822,1912,2008,7465,7558,7665,7770,7992,8117,8238,8451,8710,14891,15109,15341,15577,16026,16239,16448,21235,21436,21552,21722,22330,23359,27219,27723,28231,28740,29254,29759,30263,30768,31274,31776,32282,32791,33299,33798,34305,34813,35105,35399,35699,39285,39614,39955,40093,40237,40393,40786,41004,41226,41452,42654,42764,42934,43124,43365,43624", "endLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,22,23,24,25,86,87,88,89,91,92,95,98,101,196,199,202,205,211,214,217,284,285,288,293,309,315,379,388,397,406,415,424,433,442,451,460,469,478,487,496,505,511,517,523,529,580,584,585,586,587,591,594,597,600,603,620,623,626,630,634,637", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "325,421,517,615,683,762,850,938,1026,1114,1201,1288,1375,1462,1817,1907,2003,2093,7553,7660,7765,7884,8112,8233,8446,8705,8976,15104,15336,15572,15822,16234,16443,16674,21431,21547,21717,22038,23354,23811,27718,28226,28735,29249,29754,30258,30763,31269,31771,32277,32786,33294,33793,34300,34808,35100,35394,35694,35994,39609,39950,40088,40232,40388,40781,40999,41221,41447,41663,42759,42929,43119,43360,43619,43796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,41,43,44,45,46,48,50,51,52,53,54,56,58,60,62,64,66,67,72,74,76,77,78,80,82,83,84,85,90,102,145,148,191,206,218,220,222,224,227,231,234,235,236,239,240,241,242,243,244,247,248,250,252,254,256,260,262,263,264,265,267,271,273,275,276,277,278,279,280,316,317,318,328,329,330,342", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2098,2189,2292,2395,2500,2607,2716,2825,2934,3043,3152,3259,3362,3481,3636,3791,3896,4017,4118,4265,4406,4509,4628,4735,4838,4993,5164,5313,5478,5635,5786,5905,6256,6405,6554,6666,6813,6966,7113,7188,7277,7364,7889,8981,11739,11924,14694,15827,16679,16802,16925,17038,17221,17476,17677,17766,17877,18110,18211,18306,18429,18558,18675,18852,18951,19086,19229,19364,19483,19684,19803,19896,20007,20063,20170,20365,20476,20609,20704,20795,20886,20979,21096,23816,23887,23970,24593,24650,24708,25332", "endLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,40,42,43,44,45,47,49,50,51,52,53,55,57,59,61,63,65,66,71,73,75,76,77,79,81,82,83,84,85,90,144,147,190,193,208,219,221,223,226,230,233,234,235,238,239,240,241,242,243,246,247,249,251,253,255,259,261,262,263,264,266,270,272,274,275,276,277,278,279,281,316,317,327,328,329,341,353", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2184,2287,2390,2495,2602,2711,2820,2929,3038,3147,3254,3357,3476,3631,3786,3891,4012,4113,4260,4401,4504,4623,4730,4833,4988,5159,5308,5473,5630,5781,5900,6251,6400,6549,6661,6808,6961,7108,7183,7272,7359,7460,7987,11734,11919,14689,14886,16021,16797,16920,17033,17216,17471,17672,17761,17872,18105,18206,18301,18424,18553,18670,18847,18946,19081,19224,19359,19478,19679,19798,19891,20002,20058,20165,20360,20471,20604,20699,20790,20881,20974,21091,21230,23882,23965,24588,24645,24703,25327,25963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7cd82f1c386e1e6ccac51b8702951fe7\\transformed\\media-1.6.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "356,359,363,367", "startColumns": "4,4,4,4", "startOffsets": "26210,26378,26667,26963", "endLines": "358,361,365,369", "endColumns": "12,12,12,12", "endOffsets": "26373,26536,26830,27125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,7,13,17,20,25,29,52,55,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,418,933,1169,1377,1801,2095,3791,3989,4180", "endLines": "2,6,12,16,19,24,28,51,54,58,59", "endColumns": "88,12,12,12,12,12,12,12,12,12,72", "endOffsets": "139,413,928,1164,1372,1796,2090,3786,3984,4175,4248"}, "to": {"startLines": "370,530,534,540,544,547,552,556,612,615,619", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27130,35999,36273,36788,37024,37232,37656,37950,42192,42390,42581", "endLines": "370,533,539,543,546,551,555,576,614,618,619", "endColumns": "88,12,12,12,12,12,12,12,12,12,72", "endOffsets": "27214,36268,36783,37019,37227,37651,37945,39280,42385,42576,42649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d7a76f9f06e832d74a32eccee18e4a53\\transformed\\exoplayer-ui-2.19.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endLines": "2,7", "endColumns": "58,10", "endOffsets": "109,396"}, "to": {"startLines": "3,294", "startColumns": "4,4", "startOffsets": "173,22043", "endLines": "3,298", "endColumns": "58,10", "endOffsets": "227,22325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea86b6b5024288572ab34cbf40f675ee\\transformed\\preference-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "63", "endOffsets": "114"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "1662", "endColumns": "63", "endOffsets": "1721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\669eee2c8801e741c32a6ee27b73c23e\\transformed\\core-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,18,19,20,354,355,362,366,604,607", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1467,1531,1598,25968,26084,26541,26835,41668,41840", "endLines": "2,18,19,20,354,355,362,366,606,611", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1526,1593,1657,26079,26205,26662,26958,41835,42187"}}]}]}