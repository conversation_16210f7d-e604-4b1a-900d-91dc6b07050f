<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.tvplayer.webdav" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_home_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="263" endOffset="13"/></Target><Target id="@+id/iv_backdrop" view="ImageView"><Expressions/><location startLine="7" startOffset="4" endLine="12" endOffset="29"/></Target><Target id="@+id/tv_all_categories" view="TextView"><Expressions/><location startLine="92" startOffset="16" endLine="103" endOffset="51"/></Target><Target id="@+id/rv_categories" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="107" startOffset="12" endLine="114" endOffset="43"/></Target><Target id="@+id/tv_all_continue" view="TextView"><Expressions/><location startLine="133" startOffset="16" endLine="144" endOffset="51"/></Target><Target id="@+id/rv_continue_watching" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="148" startOffset="12" endLine="155" endOffset="43"/></Target><Target id="@+id/tv_all_recent" view="TextView"><Expressions/><location startLine="174" startOffset="16" endLine="185" endOffset="51"/></Target><Target id="@+id/rv_recently_added" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="189" startOffset="12" endLine="196" endOffset="43"/></Target><Target id="@+id/rv_movies" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="219" startOffset="12" endLine="227" endOffset="56"/></Target><Target id="@+id/rv_tv_shows" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="250" startOffset="12" endLine="258" endOffset="52"/></Target></Targets></Layout>