[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\fragment_settings.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\fragment_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\item_media_poster.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_media_poster.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\fragment_home.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\item_webdav_entry.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_webdav_entry.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\activity_video_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\activity_video_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\fragment_webdav_connection.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_webdav_connection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\fragment_scanner.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_scanner.xml"}, {"merged": "com.tvplayer.webdav.app-mergeDebugResources-62:/layout/fragment_video_details.xml", "source": "com.tvplayer.webdav.app-main-65:/layout/fragment_video_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\item_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\item_actor.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_actor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\fragment_video_details.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_video_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-mergeDebugResources-62:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\activity_main.xml"}]