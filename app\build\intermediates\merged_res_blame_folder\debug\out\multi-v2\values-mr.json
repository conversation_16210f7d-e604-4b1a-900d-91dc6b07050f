{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\127a50fba098f59f5efe01000db9ca9e\\transformed\\material-1.10.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1052,1143,1208,1267,1355,1417,1479,1539,1606,1669,1723,1837,1894,1955,2009,2079,2198,2279,2364,2499,2576,2653,2794,2880,2964,3020,3072,3138,3208,3286,3373,3455,3525,3601,3672,3741,3855,3951,4025,4123,4219,4293,4363,4465,4520,4608,4675,4762,4855,4918,4982,5045,5111,5211,5320,5414,5521,5581,5637", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,140,85,83,55,51,65,69,77,86,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77", "endOffsets": "254,339,426,509,602,686,786,902,984,1047,1138,1203,1262,1350,1412,1474,1534,1601,1664,1718,1832,1889,1950,2004,2074,2193,2274,2359,2494,2571,2648,2789,2875,2959,3015,3067,3133,3203,3281,3368,3450,3520,3596,3667,3736,3850,3946,4020,4118,4214,4288,4358,4460,4515,4603,4670,4757,4850,4913,4977,5040,5106,5206,5315,5409,5516,5576,5632,5710"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,3615,3700,3787,3870,3963,4779,4879,4995,5077,8936,9027,9092,13311,13399,13461,13523,13583,13650,13713,13767,13881,13938,13999,14053,14123,14242,14323,14408,14543,14620,14697,14838,14924,15008,15064,15116,15182,15252,15330,15417,15499,15569,15645,15716,15785,15899,15995,16069,16167,16263,16337,16407,16509,16564,16652,16719,16806,16899,16962,17026,17089,17155,17255,17364,17458,17565,17625,17997", "endLines": "22,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,140,85,83,55,51,65,69,77,86,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77", "endOffsets": "920,3695,3782,3865,3958,4042,4874,4990,5072,5135,9022,9087,9146,13394,13456,13518,13578,13645,13708,13762,13876,13933,13994,14048,14118,14237,14318,14403,14538,14615,14692,14833,14919,15003,15059,15111,15177,15247,15325,15412,15494,15564,15640,15711,15780,15894,15990,16064,16162,16258,16332,16402,16504,16559,16647,16714,16801,16894,16957,17021,17084,17150,17250,17359,17453,17560,17620,17676,18070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\669eee2c8801e741c32a6ee27b73c23e\\transformed\\core-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "55,56,57,58,59,60,61,219", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4047,4147,4251,4352,4455,4557,4662,18155", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "4142,4246,4347,4450,4552,4657,4774,18251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1036,1142,1249,1339,1440,1552,1630,1707,1798,1891,1984,2081,2181,2274,2369,2463,2554,2645,2725,2832,2933,3030,3139,3241,3355,3512,18075", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "1031,1137,1244,1334,1435,1547,1625,1702,1793,1886,1979,2076,2176,2269,2364,2458,2549,2640,2720,2827,2928,3025,3134,3236,3350,3507,3610,18150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d7a76f9f06e832d74a32eccee18e4a53\\transformed\\exoplayer-ui-2.19.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1784,1903,2014,2086,2165,2236,2306,2388,2468,2532,2595,2648,2706,2754,2815,2876,2943,3005,3071,3130,3195,3260,3325,3393,3446,3506,3580,3654", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1779,1898,2009,2081,2160,2231,2301,2383,2463,2527,2590,2643,2701,2749,2810,2871,2938,3000,3066,3125,3190,3255,3320,3388,3441,3501,3575,3649,3702"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,582,5140,5222,5302,5385,5472,5566,5634,5698,5788,5879,5944,6012,6072,6140,6253,6372,6483,6555,6634,6705,6775,6857,6937,7001,7740,7793,7851,7899,7960,8021,8088,8150,8216,8275,8340,8405,8470,8538,8591,8651,8725,8799", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "376,577,761,5217,5297,5380,5467,5561,5629,5693,5783,5874,5939,6007,6067,6135,6248,6367,6478,6550,6629,6700,6770,6852,6932,6996,7059,7788,7846,7894,7955,8016,8083,8145,8211,8270,8335,8400,8465,8533,8586,8646,8720,8794,8847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7243958f9f6ec3b8adc410641a8629a1\\transformed\\exoplayer-core-2.19.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7064,7134,7199,7268,7337,7412,7476,7573,7667", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "7129,7194,7263,7332,7407,7471,7568,7662,7735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea86b6b5024288572ab34cbf40f675ee\\transformed\\preference-1.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "105,189,358,438", "endColumns": "83,168,79,77", "endOffsets": "184,353,433,511"}, "to": {"startLines": "117,220,221,222", "startColumns": "4,4,4,4", "startOffsets": "8852,18256,18425,18505", "endColumns": "83,168,79,77", "endOffsets": "8931,18420,18500,18578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,506,626,738,835,928,1049,1169,1271,1392,1533,1655,1777,1881,1977,2107,2196,2301,2413,2516,2614,2725,2840,2951,3062,3162,3266,3379,3492,3601,3710,3797,3879,3980,4113,4265", "endColumns": "106,100,98,93,119,111,96,92,120,119,101,120,140,121,121,103,95,129,88,104,111,102,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "207,308,407,501,621,733,830,923,1044,1164,1266,1387,1528,1650,1772,1876,1972,2102,2191,2296,2408,2511,2609,2720,2835,2946,3057,3157,3261,3374,3487,3596,3705,3792,3874,3975,4108,4260,4347"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9151,9258,9359,9458,9552,9672,9784,9881,9974,10095,10215,10317,10438,10579,10701,10823,10927,11023,11153,11242,11347,11459,11562,11660,11771,11886,11997,12108,12208,12312,12425,12538,12647,12756,12843,12925,13026,13159,17910", "endColumns": "106,100,98,93,119,111,96,92,120,119,101,120,140,121,121,103,95,129,88,104,111,102,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "9253,9354,9453,9547,9667,9779,9876,9969,10090,10210,10312,10433,10574,10696,10818,10922,11018,11148,11237,11342,11454,11557,11655,11766,11881,11992,12103,12203,12307,12420,12533,12642,12751,12838,12920,13021,13154,13306,17992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae9e4a68b83caa9178cf3717646936e\\transformed\\navigation-ui-2.7.5\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "214,215", "startColumns": "4,4", "startOffsets": "17681,17793", "endColumns": "111,116", "endOffsets": "17788,17905"}}]}]}