'com.tvplayer.webdav.TVPlayerApplication)com.tvplayer.webdav.data.model.WebDAVFile3com.tvplayer.webdav.data.model.WebDAVFile.Companion+com.tvplayer.webdav.data.model.WebDAVServer2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient#com.tvplayer.webdav.di.WebDAVModule)com.tvplayer.webdav.ui.main.CardPresenter3com.tvplayer.webdav.ui.main.CardPresenter.Companion(com.tvplayer.webdav.ui.main.MainActivity(<EMAIL>;com.tvplayer.webdav.ui.webdav.Hilt_WebDAVConnectionFragment\com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel_HiltModules_KeyModule_ProvideFactorykcom.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolderCcom.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel_HiltModules3com.tvplayer.webdav.databinding.FragmentMainBindingMcom.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel_HiltModules.KeyModule^hilt_aggregated_deps._com_tvplayer_webdav_ui_webdav_WebDAVConnectionFragment_GeneratedInjectorOcom.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel_HiltModules.BindsModulechilt_aggregated_deps._com_tvplayer_webdav_ui_webdav_WebDAVConnectionViewModel_HiltModules_KeyModule?com.tvplayer.webdav.databinding.FragmentWebdavConnectionBindingehilt_aggregated_deps._com_tvplayer_webdav_ui_webdav_WebDAVConnectionViewModel_HiltModules_BindsModuleHcom.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment_GeneratedInjector?com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel_Factory(com.tvplayer.webdav.data.model.MediaItem(com.tvplayer.webdav.data.model.MediaType,com.tvplayer.webdav.data.model.MediaCategory+com.tvplayer.webdav.ui.home.CategoryAdapter><EMAIL>(com.tvplayer.webdav.ui.home.HomeFragment2com.tvplayer.webdav.ui.home.HomeFragment.Companion)com.tvplayer.webdav.ui.home.HomeViewModel.com.tvplayer.webdav.ui.home.MediaPosterAdapter><EMAIL>:com.tvplayer.webdav.ui.settings.SettingsFragment.CompanionXhilt_aggregated_deps._com_tvplayer_webdav_ui_settings_SettingsFragment_GeneratedInjector-com.tvplayer.webdav.ui.home.Hilt_HomeFragment3com.tvplayer.webdav.databinding.FragmentHomeBinding5com.tvplayer.webdav.ui.settings.Hilt_SettingsFragmentAcom.tvplayer.webdav.ui.home.HomeViewModel_HiltModules.BindsModuleUhilt_aggregated_deps._com_tvplayer_webdav_ui_home_HomeViewModel_HiltModules_KeyModule7com.tvplayer.webdav.databinding.FragmentSettingsBinding3com.tvplayer.webdav.databinding.ItemCategoryBindingWhilt_aggregated_deps._com_tvplayer_webdav_ui_home_HomeViewModel_HiltModules_BindsModule6com.tvplayer.webdav.databinding.ItemMediaPosterBinding1com.tvplayer.webdav.ui.home.HomeViewModel_FactoryNcom.tvplayer.webdav.ui.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory:com.tvplayer.webdav.ui.home.HomeFragment_GeneratedInjectorPhilt_aggregated_deps._com_tvplayer_webdav_ui_home_HomeFragment_GeneratedInjector?com.tvplayer.webdav.ui.home.HomeViewModel_HiltModules.KeyModule]com.tvplayer.webdav.ui.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolderBcom.tvplayer.webdav.ui.settings.SettingsFragment_GeneratedInjector5com.tvplayer.webdav.ui.home.HomeViewModel_HiltModules-com.tvplayer.webdav.data.scanner.MediaScanner7com.tvplayer.webdav.data.scanner.MediaScanner.CompanionBcom.tvplayer.webdav.data.scanner.MediaScanner.ScanProgressCallback,com.tvplayer.webdav.data.tmdb.TmdbApiService6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion(com.tvplayer.webdav.data.tmdb.TmdbClient2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion5com.tvplayer.webdav.data.tmdb.TmdbMovieSearchResponse'com.tvplayer.webdav.data.tmdb.TmdbMovie2com.tvplayer.webdav.data.tmdb.TmdbTVSearchResponse(com.tvplayer.webdav.data.tmdb.TmdbTVShow(com.tvplayer.webdav.data.tmdb.TmdbSeason)com.tvplayer.webdav.data.tmdb.TmdbEpisode/com.tvplayer.webdav.data.tmdb.TmdbSeasonDetails'com.tvplayer.webdav.data.tmdb.TmdbGenre/com.tvplayer.webdav.data.tmdb.TmdbConfiguration4com.tvplayer.webdav.data.tmdb.TmdbImageConfiguration/com.tvplayer.webdav.data.tmdb.TmdbErrorResponse#com.tvplayer.webdav.di.WebDAVClient!com.tvplayer.webdav.di.TmdbClient.com.tvplayer.webdav.ui.scanner.ScannerFragment8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion/com.tvplayer.webdav.ui.scanner.ScannerViewModel0com.tvplayer.webdav.data.tmdb.TmdbClient_FactoryGcom.tvplayer.webdav.ui.scanner.ScannerViewModel_HiltModules.BindsModule[hilt_aggregated_deps._com_tvplayer_webdav_ui_scanner_ScannerViewModel_HiltModules_KeyModule;com.tvplayer.webdav.ui.scanner.ScannerViewModel_HiltModules>com.tvplayer.webdav.di.WebDAVModule_ProvideWebDAVClientFactory6com.tvplayer.webdav.databinding.FragmentScannerBindingccom.tvplayer.webdav.ui.scanner.ScannerViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolderTcom.tvplayer.webdav.ui.scanner.ScannerViewModel_HiltModules_KeyModule_ProvideFactory:<EMAIL>.ScannerFragment_GeneratedInjectorEcom.tvplayer.webdav.ui.scanner.ScannerViewModel_HiltModules.KeyModuleVhilt_aggregated_deps._com_tvplayer_webdav_ui_scanner_ScannerFragment_GeneratedInjector]<EMAIL>.WebDAVModule_ProvideTmdbApiServiceFactoryDcom.tvplayer.webdav.di.WebDAVModule_ProvideWebDAVOkHttpClientFactory>com.tvplayer.webdav.di.WebDAVModule_ProvideTmdbRetrofitFactory3com.tvplayer.webdav.ui.scanner.Hilt_ScannerFragmentQcom.tvplayer.webdav.di.WebDAVModule_ProvideTmdbOkHttpClientFactory.InstanceHolder0com.tvplayer.webdav.ui.home.FocusHighlightHelper5com.tvplayer.webdav.ui.home.GridSpacingItemDecoration*com.tvplayer.webdav.ui.home.EdgeItemHelper/com.tvplayer.webdav.ui.home.PosterFocusAnimator4com.tvplayer.webdav.data.storage.WebDAVServerStorage>com.tvplayer.webdav.data.storage.WebDAVServerStorage.Companion$com.tvplayer.webdav.di.StorageModule<com.tvplayer.webdav.data.storage.WebDAVServerStorage_FactoryFcom.tvplayer.webdav.di.StorageModule_ProvideWebDAVServerStorageFactoryDcom.tvplayer.webdav.di.StorageModule_ProvideSharedPreferencesFactory:hilt_aggregated_deps._com_tvplayer_webdav_di_StorageModule6com.tvplayer.webdav.databinding.ItemWebdavEntryBinding6com.tvplayer.webdav.data.scanner.MediaScanner.ModeHint+com.tvplayer.webdav.data.storage.MediaCache"com.tvplayer.webdav.di.MediaModuleBcom.tvplayer.webdav.data.storage.MediaCache_Factory.InstanceHolder3com.tvplayer.webdav.data.storage.MediaCache_FactoryJcom.tvplayer.webdav.di.MediaModule_ProvideMediaCacheFactory.InstanceHolder;com.tvplayer.webdav.di.MediaModule_ProvideMediaCacheFactory8hilt_aggregated_deps._com_tvplayer_webdav_di_MediaModule6com.tvplayer.webdav.data.tmdb.TmdbTranslationsResponse-com.tvplayer.webdav.data.tmdb.TmdbTranslation1com.tvplayer.webdav.data.tmdb.TmdbTranslationData5com.tvplayer.webdav.data.storage.MediaCache.Companion.com.tvplayer.webdav.data.model.TVSeriesSummary+com.tvplayer.webdav.ui.home.TVSeriesAdapter><EMAIL>=com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion4com.tvplayer.webdav.ui.details.VideoDetailsViewModel;com.tvplayer.webdav.databinding.FragmentVideoDetailsBindingEcom.tvplayer.webdav.ui.details.VideoDetailsFragment_GeneratedInjectorLcom.tvplayer.webdav.ui.details.VideoDetailsViewModel_HiltModules.BindsModule[<EMAIL>.VideoDetailsViewModel_HiltModules`hilt_aggregated_deps._com_tvplayer_webdav_ui_details_VideoDetailsViewModel_HiltModules_KeyModuleKcom.tvplayer.webdav.ui.details.VideoDetailsViewModel_Factory.InstanceHolderEcom.tvplayer.webdav.ui.details.VideoDetailsActivity_GeneratedInjectorYcom.tvplayer.webdav.ui.details.VideoDetailsViewModel_HiltModules_KeyModule_ProvideFactoryhcom.tvplayer.webdav.ui.details.VideoDetailsViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolderbhilt_aggregated_deps._com_tvplayer_webdav_ui_details_VideoDetailsViewModel_HiltModules_BindsModule;com.tvplayer.webdav.databinding.ActivityVideoDetailsBinding[hilt_aggregated_deps._com_tvplayer_webdav_ui_details_VideoDetailsFragment_GeneratedInjectorJcom.tvplayer.webdav.ui.details.VideoDetailsViewModel_HiltModules.KeyModule8com.tvplayer.webdav.ui.details.Hilt_VideoDetailsActivity8com.tvplayer.webdav.ui.details.Hilt_VideoDetailsFragment<com.tvplayer.webdav.ui.details.VideoDetailsViewModel_Factory$com.tvplayer.webdav.data.model.Actor+com.tvplayer.webdav.ui.details.ActorAdapter;com.tvplayer.webdav.ui.details.ActorAdapter.ActorViewHolder0com.tvplayer.webdav.databinding.ItemActorBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   