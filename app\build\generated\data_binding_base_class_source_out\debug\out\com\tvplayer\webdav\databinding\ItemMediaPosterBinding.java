// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMediaPosterBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final ImageView ivPoster;

  @NonNull
  public final LinearLayout layoutBottomInfo;

  @NonNull
  public final LinearLayout layoutProgress;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvProgress;

  @NonNull
  public final TextView tvRating;

  @NonNull
  public final TextView tvSubtitle;

  @NonNull
  public final TextView tvTitle;

  private ItemMediaPosterBinding(@NonNull LinearLayout rootView, @NonNull CardView cardView,
      @NonNull ImageView ivPoster, @NonNull LinearLayout layoutBottomInfo,
      @NonNull LinearLayout layoutProgress, @NonNull ProgressBar progressBar,
      @NonNull TextView tvProgress, @NonNull TextView tvRating, @NonNull TextView tvSubtitle,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.cardView = cardView;
    this.ivPoster = ivPoster;
    this.layoutBottomInfo = layoutBottomInfo;
    this.layoutProgress = layoutProgress;
    this.progressBar = progressBar;
    this.tvProgress = tvProgress;
    this.tvRating = tvRating;
    this.tvSubtitle = tvSubtitle;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMediaPosterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMediaPosterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_media_poster, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMediaPosterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_view;
      CardView cardView = ViewBindings.findChildViewById(rootView, id);
      if (cardView == null) {
        break missingId;
      }

      id = R.id.iv_poster;
      ImageView ivPoster = ViewBindings.findChildViewById(rootView, id);
      if (ivPoster == null) {
        break missingId;
      }

      id = R.id.layout_bottom_info;
      LinearLayout layoutBottomInfo = ViewBindings.findChildViewById(rootView, id);
      if (layoutBottomInfo == null) {
        break missingId;
      }

      id = R.id.layout_progress;
      LinearLayout layoutProgress = ViewBindings.findChildViewById(rootView, id);
      if (layoutProgress == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_progress;
      TextView tvProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvProgress == null) {
        break missingId;
      }

      id = R.id.tv_rating;
      TextView tvRating = ViewBindings.findChildViewById(rootView, id);
      if (tvRating == null) {
        break missingId;
      }

      id = R.id.tv_subtitle;
      TextView tvSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtitle == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ItemMediaPosterBinding((LinearLayout) rootView, cardView, ivPoster,
          layoutBottomInfo, layoutProgress, progressBar, tvProgress, tvRating, tvSubtitle, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
