// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemActorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivActorAvatar;

  @NonNull
  public final TextView tvActorName;

  @NonNull
  public final TextView tvActorRole;

  private ItemActorBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivActorAvatar,
      @NonNull TextView tvActorName, @NonNull TextView tvActorRole) {
    this.rootView = rootView;
    this.ivActorAvatar = ivActorAvatar;
    this.tvActorName = tvActorName;
    this.tvActorRole = tvActorRole;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemActorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemActorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_actor, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemActorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_actor_avatar;
      ImageView ivActorAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivActorAvatar == null) {
        break missingId;
      }

      id = R.id.tv_actor_name;
      TextView tvActorName = ViewBindings.findChildViewById(rootView, id);
      if (tvActorName == null) {
        break missingId;
      }

      id = R.id.tv_actor_role;
      TextView tvActorRole = ViewBindings.findChildViewById(rootView, id);
      if (tvActorRole == null) {
        break missingId;
      }

      return new ItemActorBinding((LinearLayout) rootView, ivActorAvatar, tvActorName, tvActorRole);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
