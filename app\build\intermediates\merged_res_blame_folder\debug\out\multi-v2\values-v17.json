{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-v17/values-v17.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-v17\\values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,25,29,33,37,40,43,46,50,53,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1161,1331,1559,1783,2025,2196,2370,2539,2812,3012,3216", "endLines": "4,8,11,14,17,21,24,28,32,36,39,42,45,49,52,56,60", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1156,1326,1554,1778,2020,2191,2365,2534,2807,3007,3211,3540"}, "to": {"startLines": "15,18,22,25,28,31,35,38,42,46,50,53,56,59,63,66,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "609,782,1010,1168,1318,1490,1715,1885,2113,2337,2579,2750,2924,3093,3366,3566,3770", "endLines": "17,21,24,27,30,34,37,41,45,49,52,55,58,62,65,69,73", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "777,1005,1163,1313,1485,1710,1880,2108,2332,2574,2745,2919,3088,3361,3561,3765,4094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea86b6b5024288572ab34cbf40f675ee\\transformed\\preference-1.0.0\\res\\values-v17\\values-v17.xml", "from": {"startLines": "2,7,11", "startColumns": "4,4,4", "startOffsets": "55,280,441", "endLines": "6,10,14", "endColumns": "12,12,12", "endOffsets": "275,436,604"}}]}]}