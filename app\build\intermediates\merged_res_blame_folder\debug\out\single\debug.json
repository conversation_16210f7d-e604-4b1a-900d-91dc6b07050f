[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_frosted_glass_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\frosted_glass_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_item_media_poster.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_media_poster.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_item_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_poster_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\poster_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_fragment_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_rating_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\rating_badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_movie.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_movie.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_btn_secondary_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\btn_secondary_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_activity_video_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\activity_video_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_text_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\text_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_button_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\button_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_info_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\info_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_fragment_scanner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_scanner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_folder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_folder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_frosted_glass_advanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\frosted_glass_advanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_circle_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\circle_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_fragment_webdav_connection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_webdav_connection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_back_to_top_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\back_to_top_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_tv.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_tv.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_focus_border_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\focus_border_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_btn_play_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\btn_play_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_rating_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\rating_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_details_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\details_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_gradient_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\gradient_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_play_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_play_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_frosted_glass_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\frosted_glass_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_category_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\category_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_focus_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\focus_border.xml"}, {"merged": "com.tvplayer.webdav.app-merged_res-63:/layout_fragment_video_details.xml.flat", "source": "com.tvplayer.webdav.app-main-65:/layout/fragment_video_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_header_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\header_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_fragment_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_progress_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\progress_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_recent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_recent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_actor_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\actor_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_new_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_new_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_icon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\icon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_scan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_scan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_item_actor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_actor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_bottom_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\bottom_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_person_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_person_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_favorite.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_favorite.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_title_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\title_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_frosted_glass_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\frosted_glass_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_expand_less.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_expand_less.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_backdrop_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\backdrop_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_play.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_play.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_fragment_video_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\fragment_video_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_expand_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_expand_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\layout_item_webdav_entry.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\layout\\item_webdav_entry.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_role_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\role_badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_app_banner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\app_banner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_director_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\director_badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_frosted_glass_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\frosted_glass_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_poster_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\poster_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-merged_res-63:\\drawable_ic_star.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.tvplayer.webdav.app-main-65:\\drawable\\ic_star.xml"}]