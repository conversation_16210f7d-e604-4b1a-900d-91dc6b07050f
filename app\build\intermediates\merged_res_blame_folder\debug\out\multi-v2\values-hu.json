{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae9e4a68b83caa9178cf3717646936e\\transformed\\navigation-ui-2.7.5\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "214,215", "startColumns": "4,4", "startOffsets": "18182,18294", "endColumns": "111,119", "endOffsets": "18289,18409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\127a50fba098f59f5efe01000db9ca9e\\transformed\\material-1.10.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1033,1132,1207,1266,1376,1438,1507,1565,1637,1698,1753,1856,1913,1973,2028,2109,2229,2312,2400,2535,2618,2698,2838,2932,3014,3067,3118,3184,3260,3342,3428,3512,3589,3664,3743,3820,3925,4021,4098,4190,4287,4361,4446,4543,4595,4678,4745,4833,4920,4982,5046,5109,5175,5273,5379,5473,5580,5637,5692", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "258,339,415,492,582,662,761,881,964,1028,1127,1202,1261,1371,1433,1502,1560,1632,1693,1748,1851,1908,1968,2023,2104,2224,2307,2395,2530,2613,2693,2833,2927,3009,3062,3113,3179,3255,3337,3423,3507,3584,3659,3738,3815,3920,4016,4093,4185,4282,4356,4441,4538,4590,4673,4740,4828,4915,4977,5041,5104,5170,5268,5374,5468,5575,5632,5687,5772"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3704,3785,3861,3938,4028,4830,4929,5049,5132,9129,9228,9303,13756,13866,13928,13997,14055,14127,14188,14243,14346,14403,14463,14518,14599,14719,14802,14890,15025,15108,15188,15328,15422,15504,15557,15608,15674,15750,15832,15918,16002,16079,16154,16233,16310,16415,16511,16588,16680,16777,16851,16936,17033,17085,17168,17235,17323,17410,17472,17536,17599,17665,17763,17869,17963,18070,18127,18507", "endLines": "22,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "endColumns": "12,80,75,76,89,79,98,119,82,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,87,134,82,79,139,93,81,52,50,65,75,81,85,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84", "endOffsets": "945,3780,3856,3933,4023,4103,4924,5044,5127,5191,9223,9298,9357,13861,13923,13992,14050,14122,14183,14238,14341,14398,14458,14513,14594,14714,14797,14885,15020,15103,15183,15323,15417,15499,15552,15603,15669,15745,15827,15913,15997,16074,16149,16228,16305,16410,16506,16583,16675,16772,16846,16931,17028,17080,17163,17230,17318,17405,17467,17531,17594,17660,17758,17864,17958,18065,18122,18177,18587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea86b6b5024288572ab34cbf40f675ee\\transformed\\preference-1.0.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "105,193,362,441", "endColumns": "87,168,78,75", "endOffsets": "188,357,436,512"}, "to": {"startLines": "117,220,221,222", "startColumns": "4,4,4,4", "startOffsets": "9041,18777,18946,19025", "endColumns": "87,168,78,75", "endOffsets": "9124,18941,19020,19096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d7a76f9f06e832d74a32eccee18e4a53\\transformed\\exoplayer-ui-2.19.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1861,1972,2082,2156,2238,2312,2385,2475,2564,2632,2695,2748,2806,2854,2915,2979,3046,3110,3178,3243,3302,3367,3433,3499,3552,3617,3699,3781", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1856,1967,2077,2151,2233,2307,2380,2470,2559,2627,2690,2743,2801,2849,2910,2974,3041,3105,3173,3238,3297,3362,3428,3494,3547,3612,3694,3776,3833"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,585,5196,5284,5371,5449,5535,5638,5712,5780,5877,5978,6051,6119,6184,6252,6365,6476,6586,6660,6742,6816,6889,6979,7068,7136,7898,7951,8009,8057,8118,8182,8249,8313,8381,8446,8505,8570,8636,8702,8755,8820,8902,8984", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,65,65,52,64,81,81,56", "endOffsets": "375,580,782,5279,5366,5444,5530,5633,5707,5775,5872,5973,6046,6114,6179,6247,6360,6471,6581,6655,6737,6811,6884,6974,7063,7131,7194,7946,8004,8052,8113,8177,8244,8308,8376,8441,8500,8565,8631,8697,8750,8815,8897,8979,9036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7243958f9f6ec3b8adc410641a8629a1\\transformed\\exoplayer-core-2.19.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7199,7274,7336,7410,7482,7560,7633,7727,7817", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "7269,7331,7405,7477,7555,7628,7722,7812,7893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,506,623,735,833,931,1056,1184,1295,1416,1574,1706,1841,1951,2043,2167,2261,2368,2474,2578,2678,2795,2907,3036,3168,3277,3387,3514,3649,3770,3899,3986,4071,4187,4328,4499", "endColumns": "106,100,95,96,116,111,97,97,124,127,110,120,157,131,134,109,91,123,93,106,105,103,99,116,111,128,131,108,109,126,134,120,128,86,84,115,140,170,92", "endOffsets": "207,308,404,501,618,730,828,926,1051,1179,1290,1411,1569,1701,1836,1946,2038,2162,2256,2363,2469,2573,2673,2790,2902,3031,3163,3272,3382,3509,3644,3765,3894,3981,4066,4182,4323,4494,4587"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9362,9469,9570,9666,9763,9880,9992,10090,10188,10313,10441,10552,10673,10831,10963,11098,11208,11300,11424,11518,11625,11731,11835,11935,12052,12164,12293,12425,12534,12644,12771,12906,13027,13156,13243,13328,13444,13585,18414", "endColumns": "106,100,95,96,116,111,97,97,124,127,110,120,157,131,134,109,91,123,93,106,105,103,99,116,111,128,131,108,109,126,134,120,128,86,84,115,140,170,92", "endOffsets": "9464,9565,9661,9758,9875,9987,10085,10183,10308,10436,10547,10668,10826,10958,11093,11203,11295,11419,11513,11620,11726,11830,11930,12047,12159,12288,12420,12529,12639,12766,12901,13022,13151,13238,13323,13439,13580,13751,18502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\669eee2c8801e741c32a6ee27b73c23e\\transformed\\core-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "55,56,57,58,59,60,61,219", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4108,4205,4307,4409,4510,4613,4720,18676", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "4200,4302,4404,4505,4608,4715,4825,18772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "950,1058,1150,1265,1349,1464,1587,1664,1739,1830,1923,2018,2112,2212,2305,2400,2495,2586,2677,2760,2870,2980,3080,3191,3300,3419,3601,18592", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "1053,1145,1260,1344,1459,1582,1659,1734,1825,1918,2013,2107,2207,2300,2395,2490,2581,2672,2755,2865,2975,3075,3186,3295,3414,3596,3699,18671"}}]}]}