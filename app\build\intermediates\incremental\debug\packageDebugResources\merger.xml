<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\android-tv-player\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\android-tv-player\app\src\main\res"><file name="fade_in" path="E:\1-test\android-tv-player\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="E:\1-test\android-tv-player\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="app_banner" path="E:\1-test\android-tv-player\app\src\main\res\drawable\app_banner.xml" qualifiers="" type="drawable"/><file name="backdrop_gradient" path="E:\1-test\android-tv-player\app\src\main\res\drawable\backdrop_gradient.xml" qualifiers="" type="drawable"/><file name="bottom_gradient" path="E:\1-test\android-tv-player\app\src\main\res\drawable\bottom_gradient.xml" qualifiers="" type="drawable"/><file name="btn_play_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\btn_play_background.xml" qualifiers="" type="drawable"/><file name="button_secondary" path="E:\1-test\android-tv-player\app\src\main\res\drawable\button_secondary.xml" qualifiers="" type="drawable"/><file name="category_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\category_background.xml" qualifiers="" type="drawable"/><file name="details_gradient" path="E:\1-test\android-tv-player\app\src\main\res\drawable\details_gradient.xml" qualifiers="" type="drawable"/><file name="focus_border" path="E:\1-test\android-tv-player\app\src\main\res\drawable\focus_border.xml" qualifiers="" type="drawable"/><file name="focus_border_enhanced" path="E:\1-test\android-tv-player\app\src\main\res\drawable\focus_border_enhanced.xml" qualifiers="" type="drawable"/><file name="frosted_glass_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\frosted_glass_background.xml" qualifiers="" type="drawable"/><file name="gradient_overlay" path="E:\1-test\android-tv-player\app\src\main\res\drawable\gradient_overlay.xml" qualifiers="" type="drawable"/><file name="header_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\header_background.xml" qualifiers="" type="drawable"/><file name="icon_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\icon_background.xml" qualifiers="" type="drawable"/><file name="ic_expand_more" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_expand_more.xml" qualifiers="" type="drawable"/><file name="ic_favorite" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_favorite.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_round" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_launcher_round.xml" qualifiers="" type="drawable"/><file name="ic_movie" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_movie.xml" qualifiers="" type="drawable"/><file name="ic_new_badge" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_new_badge.xml" qualifiers="" type="drawable"/><file name="ic_play" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_play_circle" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_play_circle.xml" qualifiers="" type="drawable"/><file name="ic_recent" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_recent.xml" qualifiers="" type="drawable"/><file name="ic_scan" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_scan.xml" qualifiers="" type="drawable"/><file name="ic_search" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_star" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_star.xml" qualifiers="" type="drawable"/><file name="ic_tv" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_tv.xml" qualifiers="" type="drawable"/><file name="ic_video" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_video.xml" qualifiers="" type="drawable"/><file name="info_card_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\info_card_background.xml" qualifiers="" type="drawable"/><file name="poster_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\poster_background.xml" qualifiers="" type="drawable"/><file name="poster_item_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\poster_item_background.xml" qualifiers="" type="drawable"/><file name="progress_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\progress_background.xml" qualifiers="" type="drawable"/><file name="rating_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\rating_background.xml" qualifiers="" type="drawable"/><file name="rating_badge_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\rating_badge_background.xml" qualifiers="" type="drawable"/><file name="status_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="text_button_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\text_button_background.xml" qualifiers="" type="drawable"/><file name="title_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\title_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\1-test\android-tv-player\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_video_details" path="E:\1-test\android-tv-player\app\src\main\res\layout\activity_video_details.xml" qualifiers="" type="layout"/><file name="fragment_home" path="E:\1-test\android-tv-player\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_main" path="E:\1-test\android-tv-player\app\src\main\res\layout\fragment_main.xml" qualifiers="" type="layout"/><file name="fragment_scanner" path="E:\1-test\android-tv-player\app\src\main\res\layout\fragment_scanner.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="E:\1-test\android-tv-player\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_video_details" path="E:\1-test\android-tv-player\app\src\main\res\layout\fragment_video_details.xml" qualifiers="" type="layout"/><file name="fragment_webdav_connection" path="E:\1-test\android-tv-player\app\src\main\res\layout\fragment_webdav_connection.xml" qualifiers="" type="layout"/><file name="item_category" path="E:\1-test\android-tv-player\app\src\main\res\layout\item_category.xml" qualifiers="" type="layout"/><file name="item_media_poster" path="E:\1-test\android-tv-player\app\src\main\res\layout\item_media_poster.xml" qualifiers="" type="layout"/><file name="item_webdav_entry" path="E:\1-test\android-tv-player\app\src\main\res\layout\item_webdav_entry.xml" qualifiers="" type="layout"/><file path="E:\1-test\android-tv-player\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="lb_basic_card_bg_color">#FF263238</color><color name="lb_basic_card_title_text_color">#FFFFFFFF</color><color name="lb_basic_card_content_text_color">#FFCCCCCC</color><color name="lb_playback_controls_background_light">#33000000</color><color name="lb_playback_controls_background_dark">#33FFFFFF</color><color name="primary_color">#FF1976D2</color><color name="primary_dark_color">#FF0D47A1</color><color name="accent_color">#FF03DAC6</color><color name="accent_color_light">#FF4DDBD4</color><color name="accent_color_dark">#FF018786</color><color name="background_color">#FF0A0A0A</color><color name="surface_color">#FF1E1E1E</color><color name="error_color">#FFCF6679</color><color name="card_background">#FF1A1A1A</color><color name="card_background_focused">#FF2A2A2A</color><color name="card_background_pressed">#FF333333</color><color name="card_border">#FF333333</color><color name="text_primary">#FFFFFFFF</color><color name="text_secondary">#FFCCCCCC</color><color name="text_tertiary">#FF999999</color><color name="success_color">#FF4CAF50</color><color name="warning_color">#FFFF9800</color><color name="info_color">#FF2196F3</color></file><file path="E:\1-test\android-tv-player\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="lb_browse_padding_start">56dp</dimen><dimen name="lb_browse_padding_end">56dp</dimen><dimen name="lb_browse_padding_top">27dp</dimen><dimen name="lb_browse_padding_bottom">27dp</dimen><dimen name="lb_browse_rows_margin_start">196dp</dimen><dimen name="lb_browse_rows_margin_top">27dp</dimen><dimen name="lb_browse_rows_fading_edge">128dp</dimen><dimen name="card_width">200dp</dimen><dimen name="card_height">120dp</dimen><dimen name="card_margin">8dp</dimen><dimen name="text_size_large">24sp</dimen><dimen name="text_size_medium">18sp</dimen><dimen name="text_size_small">14sp</dimen><dimen name="icon_size_small">24dp</dimen><dimen name="icon_size_medium">32dp</dimen><dimen name="icon_size_large">48dp</dimen></file><file path="E:\1-test\android-tv-player\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TV Player</string><string name="browse_title">WebDAV TV Player</string><string name="related_movies">Related Videos</string><string name="grid_view">Grid View</string><string name="error_fragment">Error Fragment</string><string name="personal_settings">Settings</string><string name="watch_trailer_1">Watch trailer</string><string name="watch_trailer_2">FREE</string><string name="rent_1">Rent By Day</string><string name="rent_2">From $1.99</string><string name="buy_1">Buy and Own</string><string name="buy_2">AT $9.99</string><string name="movie">Movie</string><string name="should_start">shouldStart</string><string name="start_position">startPosition</string><string name="search_results">Search Results</string><string name="no_search_results">No search results found.</string><string name="settings_title">Settings</string><string name="webdav_settings">WebDAV Settings</string><string name="server_url">Server URL</string><string name="username">Username</string><string name="password">Password</string><string name="connect">Connect</string><string name="connection_failed">Connection failed</string><string name="connection_success">Connected successfully</string><string name="loading">Loading...</string><string name="no_files">No files found</string><string name="play">Play</string><string name="pause">Pause</string><string name="previous">Previous</string><string name="next">Next</string><string name="rewind">Rewind</string><string name="fast_forward">Fast Forward</string><string name="favorites">Favorites</string><string name="history">History</string><string name="add_to_favorites">Add to Favorites</string><string name="remove_from_favorites">Remove from Favorites</string></file><file path="E:\1-test\android-tv-player\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TVPlayer" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">@color/background_color</item>
        <item name="android:navigationBarColor" ns1:targetApi="l">@color/background_color</item>
        
        <item name="android:windowBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <item name="android:windowAnimationStyle">@null</item>
        
    </style><style name="Theme.TVPlayer.TV" parent="Theme.TVPlayer">
        
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style></file><file name="backup_rules" path="E:\1-test\android-tv-player\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\1-test\android-tv-player\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="E:\1-test\android-tv-player\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="actor_item_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\actor_item_background.xml" qualifiers="" type="drawable"/><file name="back_to_top_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\back_to_top_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_border" path="E:\1-test\android-tv-player\app\src\main\res\drawable\circle_border.xml" qualifiers="" type="drawable"/><file name="director_badge_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\director_badge_background.xml" qualifiers="" type="drawable"/><file name="ic_expand_less" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_expand_less.xml" qualifiers="" type="drawable"/><file name="ic_person_placeholder" path="E:\1-test\android-tv-player\app\src\main\res\drawable\ic_person_placeholder.xml" qualifiers="" type="drawable"/><file name="role_badge_background" path="E:\1-test\android-tv-player\app\src\main\res\drawable\role_badge_background.xml" qualifiers="" type="drawable"/><file name="item_actor" path="E:\1-test\android-tv-player\app\src\main\res\layout\item_actor.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\android-tv-player\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\android-tv-player\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\android-tv-player\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\android-tv-player\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>