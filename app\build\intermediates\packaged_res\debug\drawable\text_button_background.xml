<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 获得焦点时 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/accent_color" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- 按下时 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_color" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#22FFFFFF" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
</selector>
