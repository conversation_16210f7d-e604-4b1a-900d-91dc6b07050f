// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageView ivBackdrop;

  @NonNull
  public final RecyclerView rvCategories;

  @NonNull
  public final RecyclerView rvContinueWatching;

  @NonNull
  public final RecyclerView rvMovies;

  @NonNull
  public final RecyclerView rvRecentlyAdded;

  @NonNull
  public final RecyclerView rvTvShows;

  @NonNull
  public final TextView tvAllCategories;

  @NonNull
  public final TextView tvAllContinue;

  @NonNull
  public final TextView tvAllRecent;

  private FragmentHomeBinding(@NonNull FrameLayout rootView, @NonNull ImageView ivBackdrop,
      @NonNull RecyclerView rvCategories, @NonNull RecyclerView rvContinueWatching,
      @NonNull RecyclerView rvMovies, @NonNull RecyclerView rvRecentlyAdded,
      @NonNull RecyclerView rvTvShows, @NonNull TextView tvAllCategories,
      @NonNull TextView tvAllContinue, @NonNull TextView tvAllRecent) {
    this.rootView = rootView;
    this.ivBackdrop = ivBackdrop;
    this.rvCategories = rvCategories;
    this.rvContinueWatching = rvContinueWatching;
    this.rvMovies = rvMovies;
    this.rvRecentlyAdded = rvRecentlyAdded;
    this.rvTvShows = rvTvShows;
    this.tvAllCategories = tvAllCategories;
    this.tvAllContinue = tvAllContinue;
    this.tvAllRecent = tvAllRecent;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_backdrop;
      ImageView ivBackdrop = ViewBindings.findChildViewById(rootView, id);
      if (ivBackdrop == null) {
        break missingId;
      }

      id = R.id.rv_categories;
      RecyclerView rvCategories = ViewBindings.findChildViewById(rootView, id);
      if (rvCategories == null) {
        break missingId;
      }

      id = R.id.rv_continue_watching;
      RecyclerView rvContinueWatching = ViewBindings.findChildViewById(rootView, id);
      if (rvContinueWatching == null) {
        break missingId;
      }

      id = R.id.rv_movies;
      RecyclerView rvMovies = ViewBindings.findChildViewById(rootView, id);
      if (rvMovies == null) {
        break missingId;
      }

      id = R.id.rv_recently_added;
      RecyclerView rvRecentlyAdded = ViewBindings.findChildViewById(rootView, id);
      if (rvRecentlyAdded == null) {
        break missingId;
      }

      id = R.id.rv_tv_shows;
      RecyclerView rvTvShows = ViewBindings.findChildViewById(rootView, id);
      if (rvTvShows == null) {
        break missingId;
      }

      id = R.id.tv_all_categories;
      TextView tvAllCategories = ViewBindings.findChildViewById(rootView, id);
      if (tvAllCategories == null) {
        break missingId;
      }

      id = R.id.tv_all_continue;
      TextView tvAllContinue = ViewBindings.findChildViewById(rootView, id);
      if (tvAllContinue == null) {
        break missingId;
      }

      id = R.id.tv_all_recent;
      TextView tvAllRecent = ViewBindings.findChildViewById(rootView, id);
      if (tvAllRecent == null) {
        break missingId;
      }

      return new FragmentHomeBinding((FrameLayout) rootView, ivBackdrop, rvCategories,
          rvContinueWatching, rvMovies, rvRecentlyAdded, rvTvShows, tvAllCategories, tvAllContinue,
          tvAllRecent);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
