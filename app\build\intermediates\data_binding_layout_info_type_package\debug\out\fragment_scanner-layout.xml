<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_scanner" modulePackage="com.tvplayer.webdav" filePath="app\src\main\res\layout\fragment_scanner.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_scanner_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="179" endOffset="12"/></Target><Target id="@+id/tv_movies_dir" view="TextView"><Expressions/><location startLine="67" startOffset="16" endLine="73" endOffset="40"/></Target><Target id="@+id/btn_pick_movies_dir" view="Button"><Expressions/><location startLine="75" startOffset="16" endLine="80" endOffset="51"/></Target><Target id="@+id/btn_scan_movies" view="Button"><Expressions/><location startLine="82" startOffset="16" endLine="89" endOffset="44"/></Target><Target id="@+id/tv_tv_dir" view="TextView"><Expressions/><location startLine="105" startOffset="16" endLine="111" endOffset="40"/></Target><Target id="@+id/btn_pick_tv_dir" view="Button"><Expressions/><location startLine="113" startOffset="16" endLine="118" endOffset="51"/></Target><Target id="@+id/btn_scan_tv" view="Button"><Expressions/><location startLine="120" startOffset="16" endLine="127" endOffset="44"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="132" startOffset="8" endLine="141" endOffset="42"/></Target><Target id="@+id/tv_scan_progress" view="TextView"><Expressions/><location startLine="143" startOffset="8" endLine="152" endOffset="47"/></Target><Target id="@+id/tv_scan_status" view="TextView"><Expressions/><location startLine="154" startOffset="8" endLine="165" endOffset="48"/></Target></Targets></Layout>