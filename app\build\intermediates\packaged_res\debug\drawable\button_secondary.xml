<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 获得焦点时 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/accent_color" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 按下时 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_color" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#FF333333" />
            <corners android:radius="8dp" />
            <stroke
                android:width="1dp"
                android:color="#FF555555" />
        </shape>
    </item>
    
</selector>
