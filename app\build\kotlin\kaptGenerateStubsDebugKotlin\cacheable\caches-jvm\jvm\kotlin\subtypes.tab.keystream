android.os.Parcelablekotlin.Annotation(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroidx.fragment.app.Fragmentandroidx.lifecycle.ViewModelandroid.app.Applicationkotlin.Enum"androidx.leanback.widget.Presenter&androidx.fragment.app.FragmentActivity8androidx.recyclerview.widget.RecyclerView.ItemDecoration androidx.viewbinding.ViewBindingandroidx.room.RoomDatabase(androidx.appcompat.app.AppCompatActivity>com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState1androidx.recyclerview.widget.RecyclerView.Adapter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              