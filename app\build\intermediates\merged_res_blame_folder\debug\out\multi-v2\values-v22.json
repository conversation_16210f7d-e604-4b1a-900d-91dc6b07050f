{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-v22/values-v22.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,107", "endColumns": "51,53", "endOffsets": "102,156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,487", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,482,764"}, "to": {"startLines": "4,5,6,11", "startColumns": "4,4,4,4", "startOffsets": "161,236,323,593", "endLines": "4,5,10,15", "endColumns": "74,86,12,12", "endOffsets": "231,318,588,870"}}]}]}