'com.tvplayer.webdav.TVPlayerApplication(com.tvplayer.webdav.data.model.MediaItem(com.tvplayer.webdav.data.model.MediaType)com.tvplayer.webdav.data.model.WebDAVFile+com.tvplayer.webdav.data.model.WebDAVServer#com.tvplayer.webdav.di.WebDAVClient!com.tvplayer.webdav.di.TmdbClient+com.tvplayer.webdav.ui.home.CategoryAdapter><EMAIL>(com.tvplayer.webdav.ui.home.HomeFragment)com.tvplayer.webdav.ui.home.HomeViewModel.com.tvplayer.webdav.ui.home.MediaPosterAdapter><EMAIL>)com.tvplayer.webdav.ui.main.CardPresenter(com.tvplayer.webdav.ui.main.MainActivity(com.tvplayer.webdav.ui.main.MainFragment.com.tvplayer.webdav.ui.scanner.ScannerFragment/com.tvplayer.webdav.ui.scanner.ScannerViewModel0com.tvplayer.webdav.ui.settings.SettingsFragment6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel5com.tvplayer.webdav.ui.home.GridSpacingItemDecoration6com.tvplayer.webdav.databinding.ItemMediaPosterBinding3com.tvplayer.webdav.databinding.FragmentHomeBinding/com.tvplayer.webdav.data.database.MediaDatabase.com.tvplayer.webdav.ui.scraper.ScraperActivity6com.tvplayer.webdav.databinding.ActivityScraperBindingCcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.IdleIcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.ConnectingGcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.ScanningEcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.SavingHcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.CompletedDcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.Error6com.tvplayer.webdav.databinding.ItemWebdavEntryBinding6com.tvplayer.webdav.data.scanner.MediaScanner.ModeHint6com.tvplayer.webdav.databinding.FragmentScannerBinding.com.tvplayer.webdav.data.model.TVSeriesSummary+com.tvplayer.webdav.ui.home.TVSeriesAdapter><EMAIL>;com.tvplayer.webdav.databinding.FragmentVideoDetailsBinding;com.tvplayer.webdav.databinding.ActivityVideoDetailsBinding$com.tvplayer.webdav.data.model.Actor+com.tvplayer.webdav.ui.details.ActorAdapter;com.tvplayer.webdav.ui.details.ActorAdapter.ActorViewHolder0com.tvplayer.webdav.databinding.ItemActorBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             