-- Merging decision tree log ---
manifest
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:2:1-67:12
INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:2:1-67:12
INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:2:1-67:12
INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:2:1-67:12
MERGED from [androidx.databinding:databinding-adapters:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\71034674bed3d494e8fa5eaa0a9c86fb\transformed\databinding-adapters-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\76fbcde0547b0d3b275931780f80129f\transformed\databinding-ktx-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ab149d3ae65eb37506860af560286209\transformed\databinding-runtime-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5845a07c5fe5727e9c9996be2323ff32\transformed\viewbinding-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\979f39edce959622c08da26893ea39f7\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4d80f74b7b30a5791e4957a6ef870b2b\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\6ed3292f288339193401656791089157\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\46db42aaa8c36f60f03c6528194ec0d1\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\9ad97a35a67f81d69f5a7e775c840e10\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\605ad1925d0af0020454fbb08a5c1371\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\34f9c93badc6ce258dbb2ba187d6a3bb\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\2ae9e4a68b83caa9178cf3717646936e\transformed\navigation-ui-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\127a50fba098f59f5efe01000db9ca9e\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\07eb54df695345037e385a7cccf2e065\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\23a232603762dc47a30f26d57fa7e821\transformed\leanback-preference-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4170dfa5e7ce08453bce3cd31c2ae1d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c271721f60a5cb72e6dbad3f21baed0b\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea86b6b5024288572ab34cbf40f675ee\transformed\preference-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d4f28b47242b8502d63ceee6844d2d99\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b49b0b48b16cd290d5f8e8dae47d19e\transformed\leanback-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\51b245b2e647d483febf369d40f9b29f\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2b424156f8f2b097981f672424ea96a\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4338e19ddd3856cf9eb367df48ad358\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\de4c48706e59964f9c03b34c0b455d64\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db657672364f63ad03b7e18c1e016a27\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\457944c1316c27284b1491c143216abf\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\822e763bc93ee6b922ff65c1287b3d47\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4f0755644404e13ea8c9b95b4136101\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54be8d824059d42b4629ba00cb1400ca\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\94f318fe181cd7580817de0478a74d1e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a451c6e4a1be351579d9a76a6b036dd6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f625f072386e4267ef589dcaaae503e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4601f4147f888d955a5479094e398622\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\146be585473727c2584e39d94ec16e7c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec4cf48b351c3b07b9f005f623c9c70b\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d8e546df81ebcf90116483f50594ba4\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cff53432421c21c14aeff8166a345cb\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\47e86035f372991f8cd487f974353df0\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\530017db0789b242113cf6623e6b98e9\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\7243958f9f6ec3b8adc410641a8629a1\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7a76f9f06e832d74a32eccee18e4a53\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\716d57f23732a0fa01f283b93a54cf9d\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cd82f1c386e1e6ccac51b8702951fe7\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87c9543a8ae6aaa07e3710a7f23cf4ab\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48f65cd12c10f5eb5cc279ebda14939c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\309b61bca57e0b2d878d76b2e4ff4689\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7e04826a1550a7d204f57ee6c3d139bd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7fa34e17f7b5472b6ad599aff5c59ac6\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c4c91015c2e94db350ec34d4a7169f4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d4e16159fa0028447770696f9872ff0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cd4874fef9f4cdeeb54bca5852dd6bae\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b6e0d4bdab540a530ad8c139c63d173\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d757748b2eca2f1c39dffd27eedf1928\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0f93bd861c0c45af41015e7c0daad737\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\797dfc17dde569a3a53dff40360aa12c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f0066c9a6d11811b9673e34d2336629\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2213c6e6b5f5e9eecd8dc61faf025b10\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d37423262b4e36dd1e885fd9da301712\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\000f0a82d87da708cda9692befa526e6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1fa3ccaa5bb08bc52a3373b635b1e4c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ac0ee8530d51aebeaea7c96ab4ef90e\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\44396d6db8e2653d6e4f4262d7266d23\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6a9ae9f43a338df489eabfba8570aac\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\248216e2c284086ff2e29c1c721e21da\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f19e400ea838d3e65bdd440560aa7b\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aebf3db1e5ec2c9a8f4dfceb467639d\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60cd883ba1fa4a701037c2c721b022bf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\32ef028ae16e17ad9e09f96cefa7332d\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\040f47c0b31b58d1a6d8a246ef2ebabb\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\115bb6c918ad822147535f364557afa9\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8d6fbc972afc5701ac0bafd33368e3c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5e6f776b98619cbc201f4ce91bcba9a\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7d6b70dea1abe4661e445ef6069d329\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2520036df36315a43579672eb7b6768\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\36b9e1801b04b668ea7f4f505a98a761\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\a348a850ea1ec9af616904a2c3e034db\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1e1c3221e2d0576bea51f11c9ef66f4\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-3\f55ae18db1d028237ceb6957c4554c01\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4b3db2006ea039be12e50ab0c8c4e0a6\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\528fc7172ef37cbef74ae0bc81b2a2c9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4593f8e97e07cb3a06cf8d55197fed6d\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfc88d97113ae46e2c5d3e61a7dda1a9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b05e65986c2e6566db5c315be3f059b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e192e0d7ace9587d1e3e6358e2a255a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7514f08be00900e13ae29c0199373acc\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19b3ed9e0e3a115d58741842280cfc0f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a186b940e47c875dce29cc5cae3e81ab\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\a3eb4bf8b2570e9e4553374c5325f104\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\7243958f9f6ec3b8adc410641a8629a1\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\7243958f9f6ec3b8adc410641a8629a1\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1e1c3221e2d0576bea51f11c9ef66f4\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1e1c3221e2d0576bea51f11c9ef66f4\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:10:5-80
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:11:5-81
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:11:22-78
uses-feature#android.software.leanback
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:14:5-16:35
	android:required
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:16:9-32
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:15:9-49
uses-feature#android.hardware.touchscreen
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:17:5-19:36
	android:required
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:19:9-33
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:18:9-52
application
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:21:5-65:19
INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:21:5-65:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\127a50fba098f59f5efe01000db9ca9e\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\127a50fba098f59f5efe01000db9ca9e\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\07eb54df695345037e385a7cccf2e065\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\07eb54df695345037e385a7cccf2e065\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\528fc7172ef37cbef74ae0bc81b2a2c9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\528fc7172ef37cbef74ae0bc81b2a2c9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b05e65986c2e6566db5c315be3f059b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b05e65986c2e6566db5c315be3f059b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:28:9-56
	android:icon
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:26:9-45
	android:banner
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:31:9-46
	android:networkSecurityConfig
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:32:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:29:9-35
	android:label
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:27:9-41
	android:fullBackupContent
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:34:9-29
	android:allowBackup
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:23:9-35
	android:theme
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:30:9-46
	android:dataExtractionRules
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:24:9-65
	android:usesCleartextTraffic
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:33:9-44
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:22:9-44
activity#com.tvplayer.webdav.ui.main.MainActivity
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:36:9-45:20
	android:screenOrientation
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:39:13-50
	android:exported
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:38:13-36
	android:theme
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:40:13-50
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:37:13-49
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:41:13-44:29
action#android.intent.action.MAIN
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:42:17-69
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:42:25-66
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:43:17-86
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:43:27-83
activity#com.tvplayer.webdav.ui.player.PlayerActivity
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:47:9-51:53
	android:screenOrientation
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:50:13-50
	android:exported
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:49:13-37
	android:theme
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:51:13-50
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:48:13-53
activity#com.tvplayer.webdav.ui.settings.SettingsActivity
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:53:9-57:53
	android:screenOrientation
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:56:13-50
	android:exported
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:55:13-37
	android:theme
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:57:13-50
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:54:13-57
activity#com.tvplayer.webdav.ui.details.VideoDetailsActivity
ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:59:9-63:53
	android:screenOrientation
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:62:13-50
	android:exported
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:61:13-37
	android:theme
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:63:13-50
	android:name
		ADDED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml:60:13-60
uses-sdk
INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\71034674bed3d494e8fa5eaa0a9c86fb\transformed\databinding-adapters-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\71034674bed3d494e8fa5eaa0a9c86fb\transformed\databinding-adapters-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\76fbcde0547b0d3b275931780f80129f\transformed\databinding-ktx-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\76fbcde0547b0d3b275931780f80129f\transformed\databinding-ktx-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ab149d3ae65eb37506860af560286209\transformed\databinding-runtime-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ab149d3ae65eb37506860af560286209\transformed\databinding-runtime-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5845a07c5fe5727e9c9996be2323ff32\transformed\viewbinding-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5845a07c5fe5727e9c9996be2323ff32\transformed\viewbinding-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\979f39edce959622c08da26893ea39f7\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\979f39edce959622c08da26893ea39f7\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4d80f74b7b30a5791e4957a6ef870b2b\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\4d80f74b7b30a5791e4957a6ef870b2b\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\6ed3292f288339193401656791089157\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\6ed3292f288339193401656791089157\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\46db42aaa8c36f60f03c6528194ec0d1\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\46db42aaa8c36f60f03c6528194ec0d1\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\9ad97a35a67f81d69f5a7e775c840e10\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\9ad97a35a67f81d69f5a7e775c840e10\transformed\navigation-fragment-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\605ad1925d0af0020454fbb08a5c1371\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\605ad1925d0af0020454fbb08a5c1371\transformed\navigation-fragment-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\34f9c93badc6ce258dbb2ba187d6a3bb\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\34f9c93badc6ce258dbb2ba187d6a3bb\transformed\navigation-ui-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\2ae9e4a68b83caa9178cf3717646936e\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.5] C:\Users\<USER>\.gradle\caches\transforms-3\2ae9e4a68b83caa9178cf3717646936e\transformed\navigation-ui-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\127a50fba098f59f5efe01000db9ca9e\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\127a50fba098f59f5efe01000db9ca9e\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\07eb54df695345037e385a7cccf2e065\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\07eb54df695345037e385a7cccf2e065\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\23a232603762dc47a30f26d57fa7e821\transformed\leanback-preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\23a232603762dc47a30f26d57fa7e821\transformed\leanback-preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4170dfa5e7ce08453bce3cd31c2ae1d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4170dfa5e7ce08453bce3cd31c2ae1d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c271721f60a5cb72e6dbad3f21baed0b\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c271721f60a5cb72e6dbad3f21baed0b\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea86b6b5024288572ab34cbf40f675ee\transformed\preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ea86b6b5024288572ab34cbf40f675ee\transformed\preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d4f28b47242b8502d63ceee6844d2d99\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d4f28b47242b8502d63ceee6844d2d99\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b49b0b48b16cd290d5f8e8dae47d19e\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b49b0b48b16cd290d5f8e8dae47d19e\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\51b245b2e647d483febf369d40f9b29f\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\51b245b2e647d483febf369d40f9b29f\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2b424156f8f2b097981f672424ea96a\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-3\c2b424156f8f2b097981f672424ea96a\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4338e19ddd3856cf9eb367df48ad358\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4338e19ddd3856cf9eb367df48ad358\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\de4c48706e59964f9c03b34c0b455d64\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\de4c48706e59964f9c03b34c0b455d64\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db657672364f63ad03b7e18c1e016a27\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\db657672364f63ad03b7e18c1e016a27\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\457944c1316c27284b1491c143216abf\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\457944c1316c27284b1491c143216abf\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\822e763bc93ee6b922ff65c1287b3d47\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\822e763bc93ee6b922ff65c1287b3d47\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4f0755644404e13ea8c9b95b4136101\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4f0755644404e13ea8c9b95b4136101\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54be8d824059d42b4629ba00cb1400ca\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54be8d824059d42b4629ba00cb1400ca\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\94f318fe181cd7580817de0478a74d1e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\94f318fe181cd7580817de0478a74d1e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a451c6e4a1be351579d9a76a6b036dd6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a451c6e4a1be351579d9a76a6b036dd6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f625f072386e4267ef589dcaaae503e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f625f072386e4267ef589dcaaae503e\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4601f4147f888d955a5479094e398622\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4601f4147f888d955a5479094e398622\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\146be585473727c2584e39d94ec16e7c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\146be585473727c2584e39d94ec16e7c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec4cf48b351c3b07b9f005f623c9c70b\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec4cf48b351c3b07b9f005f623c9c70b\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d8e546df81ebcf90116483f50594ba4\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\9d8e546df81ebcf90116483f50594ba4\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cff53432421c21c14aeff8166a345cb\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cff53432421c21c14aeff8166a345cb\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\47e86035f372991f8cd487f974353df0\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\47e86035f372991f8cd487f974353df0\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\530017db0789b242113cf6623e6b98e9\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\530017db0789b242113cf6623e6b98e9\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\7243958f9f6ec3b8adc410641a8629a1\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\7243958f9f6ec3b8adc410641a8629a1\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7a76f9f06e832d74a32eccee18e4a53\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\d7a76f9f06e832d74a32eccee18e4a53\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\716d57f23732a0fa01f283b93a54cf9d\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\716d57f23732a0fa01f283b93a54cf9d\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cd82f1c386e1e6ccac51b8702951fe7\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cd82f1c386e1e6ccac51b8702951fe7\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87c9543a8ae6aaa07e3710a7f23cf4ab\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87c9543a8ae6aaa07e3710a7f23cf4ab\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48f65cd12c10f5eb5cc279ebda14939c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48f65cd12c10f5eb5cc279ebda14939c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\309b61bca57e0b2d878d76b2e4ff4689\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\309b61bca57e0b2d878d76b2e4ff4689\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7e04826a1550a7d204f57ee6c3d139bd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7e04826a1550a7d204f57ee6c3d139bd\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7fa34e17f7b5472b6ad599aff5c59ac6\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7fa34e17f7b5472b6ad599aff5c59ac6\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c4c91015c2e94db350ec34d4a7169f4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c4c91015c2e94db350ec34d4a7169f4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d4e16159fa0028447770696f9872ff0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d4e16159fa0028447770696f9872ff0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cd4874fef9f4cdeeb54bca5852dd6bae\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\cd4874fef9f4cdeeb54bca5852dd6bae\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b6e0d4bdab540a530ad8c139c63d173\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b6e0d4bdab540a530ad8c139c63d173\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d757748b2eca2f1c39dffd27eedf1928\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d757748b2eca2f1c39dffd27eedf1928\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0f93bd861c0c45af41015e7c0daad737\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0f93bd861c0c45af41015e7c0daad737\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\797dfc17dde569a3a53dff40360aa12c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\797dfc17dde569a3a53dff40360aa12c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f0066c9a6d11811b9673e34d2336629\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f0066c9a6d11811b9673e34d2336629\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2213c6e6b5f5e9eecd8dc61faf025b10\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2213c6e6b5f5e9eecd8dc61faf025b10\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d37423262b4e36dd1e885fd9da301712\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d37423262b4e36dd1e885fd9da301712\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\000f0a82d87da708cda9692befa526e6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\000f0a82d87da708cda9692befa526e6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1fa3ccaa5bb08bc52a3373b635b1e4c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1fa3ccaa5bb08bc52a3373b635b1e4c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ac0ee8530d51aebeaea7c96ab4ef90e\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ac0ee8530d51aebeaea7c96ab4ef90e\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\44396d6db8e2653d6e4f4262d7266d23\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\44396d6db8e2653d6e4f4262d7266d23\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6a9ae9f43a338df489eabfba8570aac\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6a9ae9f43a338df489eabfba8570aac\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\248216e2c284086ff2e29c1c721e21da\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\248216e2c284086ff2e29c1c721e21da\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f19e400ea838d3e65bdd440560aa7b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\89f19e400ea838d3e65bdd440560aa7b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aebf3db1e5ec2c9a8f4dfceb467639d\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aebf3db1e5ec2c9a8f4dfceb467639d\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60cd883ba1fa4a701037c2c721b022bf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60cd883ba1fa4a701037c2c721b022bf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\32ef028ae16e17ad9e09f96cefa7332d\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\32ef028ae16e17ad9e09f96cefa7332d\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\040f47c0b31b58d1a6d8a246ef2ebabb\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\040f47c0b31b58d1a6d8a246ef2ebabb\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\115bb6c918ad822147535f364557afa9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\115bb6c918ad822147535f364557afa9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8d6fbc972afc5701ac0bafd33368e3c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8d6fbc972afc5701ac0bafd33368e3c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5e6f776b98619cbc201f4ce91bcba9a\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\d5e6f776b98619cbc201f4ce91bcba9a\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7d6b70dea1abe4661e445ef6069d329\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7d6b70dea1abe4661e445ef6069d329\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2520036df36315a43579672eb7b6768\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2520036df36315a43579672eb7b6768\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\36b9e1801b04b668ea7f4f505a98a761\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\36b9e1801b04b668ea7f4f505a98a761\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\a348a850ea1ec9af616904a2c3e034db\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\a348a850ea1ec9af616904a2c3e034db\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1e1c3221e2d0576bea51f11c9ef66f4\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1e1c3221e2d0576bea51f11c9ef66f4\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-3\f55ae18db1d028237ceb6957c4554c01\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-3\f55ae18db1d028237ceb6957c4554c01\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4b3db2006ea039be12e50ab0c8c4e0a6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4b3db2006ea039be12e50ab0c8c4e0a6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\528fc7172ef37cbef74ae0bc81b2a2c9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\528fc7172ef37cbef74ae0bc81b2a2c9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4593f8e97e07cb3a06cf8d55197fed6d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4593f8e97e07cb3a06cf8d55197fed6d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfc88d97113ae46e2c5d3e61a7dda1a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfc88d97113ae46e2c5d3e61a7dda1a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b05e65986c2e6566db5c315be3f059b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6b05e65986c2e6566db5c315be3f059b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e192e0d7ace9587d1e3e6358e2a255a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e192e0d7ace9587d1e3e6358e2a255a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7514f08be00900e13ae29c0199373acc\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7514f08be00900e13ae29c0199373acc\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19b3ed9e0e3a115d58741842280cfc0f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19b3ed9e0e3a115d58741842280cfc0f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a186b940e47c875dce29cc5cae3e81ab\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a186b940e47c875dce29cc5cae3e81ab\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\a3eb4bf8b2570e9e4553374c5325f104\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\a3eb4bf8b2570e9e4553374c5325f104\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\1-test\android-tv-player\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\528fc7172ef37cbef74ae0bc81b2a2c9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\528fc7172ef37cbef74ae0bc81b2a2c9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd7df2e181290c615de5c2a50432a1e9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a1df1e225043b2b23cdad974e9a3a3b4\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.tvplayer.webdav.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.tvplayer.webdav.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\669eee2c8801e741c32a6ee27b73c23e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\be123343bda6542eff3640927231f03a\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\105fa33baf1a528972badf532f07535a\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fadfd149bb762c9671499cd55f7482dd\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
