{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-ldrtl-v17/values-ldrtl-v17.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,178", "endColumns": "77,44,46", "endOffsets": "128,173,220"}, "to": {"startLines": "2,5,6", "startColumns": "4,4,4", "startOffsets": "55,410,455", "endColumns": "77,44,46", "endOffsets": "128,450,497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\127a50fba098f59f5efe01000db9ca9e\\transformed\\material-1.10.0\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,191", "endColumns": "135,140", "endOffsets": "186,327"}, "to": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "133,269", "endColumns": "135,140", "endOffsets": "264,405"}}]}]}