<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_media_poster" modulePackage="com.tvplayer.webdav" filePath="app\src\main\res\layout\item_media_poster.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_media_poster_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="128" endOffset="14"/></Target><Target id="@+id/card_view" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="11" startOffset="4" endLine="85" endOffset="39"/></Target><Target id="@+id/iv_poster" view="ImageView"><Expressions/><location startLine="28" startOffset="12" endLine="33" endOffset="50"/></Target><Target id="@+id/tv_rating" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="49" endOffset="43"/></Target><Target id="@+id/layout_progress" view="LinearLayout"><Expressions/><location startLine="52" startOffset="12" endLine="81" endOffset="26"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="63" startOffset="16" endLine="70" endOffset="41"/></Target><Target id="@+id/tv_progress" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="52"/></Target><Target id="@+id/layout_bottom_info" view="LinearLayout"><Expressions/><location startLine="88" startOffset="4" endLine="126" endOffset="18"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="99" startOffset="8" endLine="110" endOffset="39"/></Target><Target id="@+id/tv_subtitle" view="TextView"><Expressions/><location startLine="112" startOffset="8" endLine="124" endOffset="39"/></Target></Targets></Layout>