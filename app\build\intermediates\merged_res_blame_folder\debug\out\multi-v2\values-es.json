{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\669eee2c8801e741c32a6ee27b73c23e\\transformed\\core-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "55,56,57,58,59,60,61,219", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4106,4205,4307,4407,4505,4612,4718,18639", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4200,4302,4402,4500,4607,4713,4833,18735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "931,1033,1146,1254,1339,1440,1568,1654,1735,1827,1921,2018,2112,2212,2306,2402,2498,2590,2682,2764,2871,2982,3081,3189,3297,3404,3563,18556", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "1028,1141,1249,1334,1435,1563,1649,1730,1822,1916,2013,2107,2207,2301,2397,2493,2585,2677,2759,2866,2977,3076,3184,3292,3399,3558,3657,18634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d7a76f9f06e832d74a32eccee18e4a53\\transformed\\exoplayer-ui-2.19.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3391,3457,3510,3575,3654,3733", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3386,3452,3505,3570,3649,3728,3786"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,566,5210,5296,5383,5468,5564,5660,5735,5803,5898,5993,6059,6128,6194,6265,6373,6479,6586,6656,6743,6813,6893,6983,7074,7140,7875,7928,7986,8034,8093,8158,8220,8286,8358,8422,8483,8549,8614,8680,8733,8798,8877,8956", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "375,561,748,5291,5378,5463,5559,5655,5730,5798,5893,5988,6054,6123,6189,6260,6368,6474,6581,6651,6738,6808,6888,6978,7069,7135,7199,7923,7981,8029,8088,8153,8215,8281,8353,8417,8478,8544,8609,8675,8728,8793,8872,8951,9009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7243958f9f6ec3b8adc410641a8629a1\\transformed\\exoplayer-core-2.19.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7204,7287,7350,7415,7489,7566,7633,7720,7806", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "7282,7345,7410,7484,7561,7628,7715,7801,7870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea86b6b5024288572ab34cbf40f675ee\\transformed\\preference-1.0.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "105,192,361,449", "endColumns": "86,168,87,81", "endOffsets": "187,356,444,526"}, "to": {"startLines": "117,220,221,222", "startColumns": "4,4,4,4", "startOffsets": "9014,18740,18909,18997", "endColumns": "86,168,87,81", "endOffsets": "9096,18904,18992,19074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,636,751,852,944,1072,1197,1309,1437,1586,1711,1833,1938,2030,2158,2253,2356,2458,2560,2656,2769,2885,3018,3148,3254,3363,3480,3602,3714,3831,3918,4012,4114,4248,4401", "endColumns": "106,100,98,96,126,114,100,91,127,124,111,127,148,124,121,104,91,127,94,102,101,101,95,112,115,132,129,105,108,116,121,111,116,86,93,101,133,152,89", "endOffsets": "207,308,407,504,631,746,847,939,1067,1192,1304,1432,1581,1706,1828,1933,2025,2153,2248,2351,2453,2555,2651,2764,2880,3013,3143,3249,3358,3475,3597,3709,3826,3913,4007,4109,4243,4396,4486"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9340,9447,9548,9647,9744,9871,9986,10087,10179,10307,10432,10544,10672,10821,10946,11068,11173,11265,11393,11488,11591,11693,11795,11891,12004,12120,12253,12383,12489,12598,12715,12837,12949,13066,13153,13247,13349,13483,18383", "endColumns": "106,100,98,96,126,114,100,91,127,124,111,127,148,124,121,104,91,127,94,102,101,101,95,112,115,132,129,105,108,116,121,111,116,86,93,101,133,152,89", "endOffsets": "9442,9543,9642,9739,9866,9981,10082,10174,10302,10427,10539,10667,10816,10941,11063,11168,11260,11388,11483,11586,11688,11790,11886,11999,12115,12248,12378,12484,12593,12710,12832,12944,13061,13148,13242,13344,13478,13631,18468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\127a50fba098f59f5efe01000db9ca9e\\transformed\\material-1.10.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2949,3039,3119,3175,3231,3297,3376,3458,3546,3635,3709,3786,3856,3935,4035,4119,4203,4295,4395,4469,4550,4652,4705,4790,4857,4950,5039,5101,5165,5228,5296,5409,5516,5620,5721,5781,5841", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2944,3034,3114,3170,3226,3292,3371,3453,3541,3630,3704,3781,3851,3930,4030,4114,4198,4290,4390,4464,4545,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5404,5511,5615,5716,5776,5836,5919"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "753,3662,3743,3822,3909,4010,4838,4942,5064,5145,9101,9196,9277,13636,13725,13789,13858,13921,13995,14059,14115,14233,14291,14353,14409,14489,14628,14717,14799,14940,15021,15101,15252,15342,15422,15478,15534,15600,15679,15761,15849,15938,16012,16089,16159,16238,16338,16422,16506,16598,16698,16772,16853,16955,17008,17093,17160,17253,17342,17404,17468,17531,17599,17712,17819,17923,18024,18084,18473", "endLines": "22,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "926,3738,3817,3904,4005,4101,4937,5059,5140,5205,9191,9272,9335,13720,13784,13853,13916,13990,14054,14110,14228,14286,14348,14404,14484,14623,14712,14794,14935,15016,15096,15247,15337,15417,15473,15529,15595,15674,15756,15844,15933,16007,16084,16154,16233,16333,16417,16501,16593,16693,16767,16848,16950,17003,17088,17155,17248,17337,17399,17463,17526,17594,17707,17814,17918,18019,18079,18139,18551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae9e4a68b83caa9178cf3717646936e\\transformed\\navigation-ui-2.7.5\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,174", "endColumns": "118,119", "endOffsets": "169,289"}, "to": {"startLines": "214,215", "startColumns": "4,4", "startOffsets": "18144,18263", "endColumns": "118,119", "endOffsets": "18258,18378"}}]}]}