android.os.Parcelableandroid.app.Application"androidx.leanback.widget.Presenter&androidx.fragment.app.FragmentActivityandroidx.fragment.app.Fragment androidx.viewbinding.ViewBindingdagger.internal.Factoryandroidx.lifecycle.ViewModel4dagger.hilt.internal.GeneratedComponentManagerHolder(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackkotlin.Enumkotlin.Annotation8androidx.recyclerview.widget.RecyclerView.ItemDecoration1androidx.recyclerview.widget.RecyclerView.Adapter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    