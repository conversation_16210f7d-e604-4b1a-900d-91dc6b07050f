<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 聚焦状态 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/accent_color" />
            <corners android:radius="8dp" />
            <stroke android:width="2dp" android:color="@color/accent_color_light" />
        </shape>
    </item>
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/accent_color_dark" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/accent_color" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
