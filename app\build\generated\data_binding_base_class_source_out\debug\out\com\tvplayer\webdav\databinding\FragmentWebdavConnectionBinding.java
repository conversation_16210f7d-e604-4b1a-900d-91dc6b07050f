// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWebdavConnectionBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnConnect;

  @NonNull
  public final Button btnTest;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final TextInputEditText etServerName;

  @NonNull
  public final TextInputEditText etServerUrl;

  @NonNull
  public final TextInputEditText etUsername;

  @NonNull
  public final TextView tvStatus;

  private FragmentWebdavConnectionBinding(@NonNull ScrollView rootView, @NonNull Button btnConnect,
      @NonNull Button btnTest, @NonNull TextInputEditText etPassword,
      @NonNull TextInputEditText etServerName, @NonNull TextInputEditText etServerUrl,
      @NonNull TextInputEditText etUsername, @NonNull TextView tvStatus) {
    this.rootView = rootView;
    this.btnConnect = btnConnect;
    this.btnTest = btnTest;
    this.etPassword = etPassword;
    this.etServerName = etServerName;
    this.etServerUrl = etServerUrl;
    this.etUsername = etUsername;
    this.tvStatus = tvStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWebdavConnectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWebdavConnectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_webdav_connection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWebdavConnectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_connect;
      Button btnConnect = ViewBindings.findChildViewById(rootView, id);
      if (btnConnect == null) {
        break missingId;
      }

      id = R.id.btn_test;
      Button btnTest = ViewBindings.findChildViewById(rootView, id);
      if (btnTest == null) {
        break missingId;
      }

      id = R.id.et_password;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.et_server_name;
      TextInputEditText etServerName = ViewBindings.findChildViewById(rootView, id);
      if (etServerName == null) {
        break missingId;
      }

      id = R.id.et_server_url;
      TextInputEditText etServerUrl = ViewBindings.findChildViewById(rootView, id);
      if (etServerUrl == null) {
        break missingId;
      }

      id = R.id.et_username;
      TextInputEditText etUsername = ViewBindings.findChildViewById(rootView, id);
      if (etUsername == null) {
        break missingId;
      }

      id = R.id.tv_status;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      return new FragmentWebdavConnectionBinding((ScrollView) rootView, btnConnect, btnTest,
          etPassword, etServerName, etServerUrl, etUsername, tvStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
