<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="12dp"
    android:layout_marginEnd="16dp"
    android:layout_marginVertical="8dp"
    android:background="@drawable/category_background"
    android:focusable="true"
    android:clickable="true"
    android:minWidth="120dp"
    android:minHeight="100dp">

    <ImageView
        android:id="@+id/iv_category_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_movie"
        android:layout_marginBottom="8dp"
        android:tint="@color/accent_color" />

    <TextView
        android:id="@+id/tv_category_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="电影"
        android:textSize="14sp"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:gravity="center" />

    <TextView
        android:id="@+id/tv_category_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="12"
        android:textSize="12sp"
        android:textColor="@color/accent_color"
        android:layout_marginTop="4dp"
        android:visibility="gone" />

</LinearLayout>
