<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 获得焦点时的状态 -->
    <item android:state_focused="true"
        android:drawable="@drawable/focus_border_enhanced" />

    <!-- 按下时的状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="16dp" />
            <stroke
                android:width="2dp"
                android:color="@color/primary_color" />
        </shape>
    </item>

    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="16dp" />
        </shape>
    </item>
</selector>
