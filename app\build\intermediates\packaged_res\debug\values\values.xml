<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="accent_color">#FF03DAC6</color>
    <color name="accent_color_dark">#FF018786</color>
    <color name="accent_color_light">#FF4DDBD4</color>
    <color name="background_color">#FF0A0A0A</color>
    <color name="black">#FF000000</color>
    <color name="card_background">#FF1A1A1A</color>
    <color name="card_background_focused">#FF2A2A2A</color>
    <color name="card_background_pressed">#FF333333</color>
    <color name="card_border">#FF333333</color>
    <color name="error_color">#FFCF6679</color>
    <color name="info_color">#FF2196F3</color>
    <color name="lb_basic_card_bg_color">#FF263238</color>
    <color name="lb_basic_card_content_text_color">#FFCCCCCC</color>
    <color name="lb_basic_card_title_text_color">#FFFFFFFF</color>
    <color name="lb_playback_controls_background_dark">#33FFFFFF</color>
    <color name="lb_playback_controls_background_light">#33000000</color>
    <color name="primary_color">#FF1976D2</color>
    <color name="primary_dark_color">#FF0D47A1</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="success_color">#FF4CAF50</color>
    <color name="surface_color">#FF1E1E1E</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_primary">#FFFFFFFF</color>
    <color name="text_secondary">#FFCCCCCC</color>
    <color name="text_tertiary">#FF999999</color>
    <color name="warning_color">#FFFF9800</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="card_height">120dp</dimen>
    <dimen name="card_margin">8dp</dimen>
    <dimen name="card_width">200dp</dimen>
    <dimen name="icon_size_large">48dp</dimen>
    <dimen name="icon_size_medium">32dp</dimen>
    <dimen name="icon_size_small">24dp</dimen>
    <dimen name="lb_browse_padding_bottom">27dp</dimen>
    <dimen name="lb_browse_padding_end">56dp</dimen>
    <dimen name="lb_browse_padding_start">56dp</dimen>
    <dimen name="lb_browse_padding_top">27dp</dimen>
    <dimen name="lb_browse_rows_fading_edge">128dp</dimen>
    <dimen name="lb_browse_rows_margin_start">196dp</dimen>
    <dimen name="lb_browse_rows_margin_top">27dp</dimen>
    <dimen name="text_size_large">24sp</dimen>
    <dimen name="text_size_medium">18sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <string name="add_to_favorites">Add to Favorites</string>
    <string name="app_name">TV Player</string>
    <string name="browse_title">WebDAV TV Player</string>
    <string name="buy_1">Buy and Own</string>
    <string name="buy_2">AT $9.99</string>
    <string name="connect">Connect</string>
    <string name="connection_failed">Connection failed</string>
    <string name="connection_success">Connected successfully</string>
    <string name="error_fragment">Error Fragment</string>
    <string name="fast_forward">Fast Forward</string>
    <string name="favorites">Favorites</string>
    <string name="grid_view">Grid View</string>
    <string name="history">History</string>
    <string name="loading">Loading...</string>
    <string name="movie">Movie</string>
    <string name="next">Next</string>
    <string name="no_files">No files found</string>
    <string name="no_search_results">No search results found.</string>
    <string name="password">Password</string>
    <string name="pause">Pause</string>
    <string name="personal_settings">Settings</string>
    <string name="play">Play</string>
    <string name="previous">Previous</string>
    <string name="related_movies">Related Videos</string>
    <string name="remove_from_favorites">Remove from Favorites</string>
    <string name="rent_1">Rent By Day</string>
    <string name="rent_2">From $1.99</string>
    <string name="rewind">Rewind</string>
    <string name="search_results">Search Results</string>
    <string name="server_url">Server URL</string>
    <string name="settings_title">Settings</string>
    <string name="should_start">shouldStart</string>
    <string name="start_position">startPosition</string>
    <string name="username">Username</string>
    <string name="watch_trailer_1">Watch trailer</string>
    <string name="watch_trailer_2">FREE</string>
    <string name="webdav_settings">WebDAV Settings</string>
    <style name="Theme.TVPlayer" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark_color</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent_color</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">@color/background_color</item>
        <item name="android:navigationBarColor" ns1:targetApi="l">@color/background_color</item>
        
        <item name="android:windowBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <item name="android:windowAnimationStyle">@null</item>
        
    </style>
    <style name="Theme.TVPlayer.TV" parent="Theme.TVPlayer">
        
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>