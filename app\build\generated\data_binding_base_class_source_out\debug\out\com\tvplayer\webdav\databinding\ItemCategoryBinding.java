// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCategoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivCategoryIcon;

  @NonNull
  public final TextView tvCategoryCount;

  @NonNull
  public final TextView tvCategoryName;

  private ItemCategoryBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivCategoryIcon,
      @NonNull TextView tvCategoryCount, @NonNull TextView tvCategoryName) {
    this.rootView = rootView;
    this.ivCategoryIcon = ivCategoryIcon;
    this.tvCategoryCount = tvCategoryCount;
    this.tvCategoryName = tvCategoryName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_category_icon;
      ImageView ivCategoryIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivCategoryIcon == null) {
        break missingId;
      }

      id = R.id.tv_category_count;
      TextView tvCategoryCount = ViewBindings.findChildViewById(rootView, id);
      if (tvCategoryCount == null) {
        break missingId;
      }

      id = R.id.tv_category_name;
      TextView tvCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (tvCategoryName == null) {
        break missingId;
      }

      return new ItemCategoryBinding((LinearLayout) rootView, ivCategoryIcon, tvCategoryCount,
          tvCategoryName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
