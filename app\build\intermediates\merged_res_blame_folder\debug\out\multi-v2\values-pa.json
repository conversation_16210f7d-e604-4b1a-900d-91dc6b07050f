{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d7a76f9f06e832d74a32eccee18e4a53\\transformed\\exoplayer-ui-2.19.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,667,756,843,926,1017,1111,1182,1245,1336,1427,1491,1554,1614,1682,1790,1907,2020,2090,2166,2237,2308,2394,2478,2544,2607,2660,2718,2766,2827,2887,2959,3021,3083,3144,3206,3271,3335,3401,3453,3513,3587,3661", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "279,482,662,751,838,921,1012,1106,1177,1240,1331,1422,1486,1549,1609,1677,1785,1902,2015,2085,2161,2232,2303,2389,2473,2539,2602,2655,2713,2761,2822,2882,2954,3016,3078,3139,3201,3266,3330,3396,3448,3508,3582,3656,3708"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,582,5112,5201,5288,5371,5462,5556,5627,5690,5781,5872,5936,5999,6059,6127,6235,6352,6465,6535,6611,6682,6753,6839,6923,6989,7752,7805,7863,7911,7972,8032,8104,8166,8228,8289,8351,8416,8480,8546,8598,8658,8732,8806", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "374,577,757,5196,5283,5366,5457,5551,5622,5685,5776,5867,5931,5994,6054,6122,6230,6347,6460,6530,6606,6677,6748,6834,6918,6984,7047,7800,7858,7906,7967,8027,8099,8161,8223,8284,8346,8411,8475,8541,8593,8653,8727,8801,8853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7243958f9f6ec3b8adc410641a8629a1\\transformed\\exoplayer-core-2.19.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7052,7123,7188,7265,7331,7406,7472,7571,7667", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "7118,7183,7260,7326,7401,7467,7566,7662,7747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,624,735,831,927,1052,1177,1288,1415,1548,1676,1804,1909,1999,2133,2222,2322,2432,2537,2633,2746,2853,2968,3083,3186,3294,3414,3534,3645,3756,3843,3925,4022,4155,4303", "endColumns": "106,100,98,95,115,110,95,95,124,124,110,126,132,127,127,104,89,133,88,99,109,104,95,112,106,114,114,102,107,119,119,110,110,86,81,96,132,147,86", "endOffsets": "207,308,407,503,619,730,826,922,1047,1172,1283,1410,1543,1671,1799,1904,1994,2128,2217,2317,2427,2532,2628,2741,2848,2963,3078,3181,3289,3409,3529,3640,3751,3838,3920,4017,4150,4298,4385"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9161,9268,9369,9468,9564,9680,9791,9887,9983,10108,10233,10344,10471,10604,10732,10860,10965,11055,11189,11278,11378,11488,11593,11689,11802,11909,12024,12139,12242,12350,12470,12590,12701,12812,12899,12981,13078,13211,17873", "endColumns": "106,100,98,95,115,110,95,95,124,124,110,126,132,127,127,104,89,133,88,99,109,104,95,112,106,114,114,102,107,119,119,110,110,86,81,96,132,147,86", "endOffsets": "9263,9364,9463,9559,9675,9786,9882,9978,10103,10228,10339,10466,10599,10727,10855,10960,11050,11184,11273,11373,11483,11588,11684,11797,11904,12019,12134,12237,12345,12465,12585,12696,12807,12894,12976,13073,13206,13354,17955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea86b6b5024288572ab34cbf40f675ee\\transformed\\preference-1.0.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "105,188,357,437", "endColumns": "82,168,79,77", "endOffsets": "183,352,432,510"}, "to": {"startLines": "117,220,221,222", "startColumns": "4,4,4,4", "startOffsets": "8858,18218,18387,18467", "endColumns": "82,168,79,77", "endOffsets": "8936,18382,18462,18540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae9e4a68b83caa9178cf3717646936e\\transformed\\navigation-ui-2.7.5\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,117", "endOffsets": "165,283"}, "to": {"startLines": "214,215", "startColumns": "4,4", "startOffsets": "17640,17755", "endColumns": "114,117", "endOffsets": "17750,17868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\127a50fba098f59f5efe01000db9ca9e\\transformed\\material-1.10.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1055,1147,1216,1275,1360,1423,1485,1543,1607,1668,1722,1836,1894,1954,2008,2078,2205,2286,2365,2500,2576,2653,2782,2866,2948,3003,3058,3124,3193,3270,3356,3435,3503,3579,3649,3714,3816,3911,3984,4078,4171,4245,4314,4408,4464,4547,4614,4698,4786,4848,4912,4975,5042,5139,5245,5336,5438,5497,5556", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "258,335,414,495,594,683,791,903,986,1050,1142,1211,1270,1355,1418,1480,1538,1602,1663,1717,1831,1889,1949,2003,2073,2200,2281,2360,2495,2571,2648,2777,2861,2943,2998,3053,3119,3188,3265,3351,3430,3498,3574,3644,3709,3811,3906,3979,4073,4166,4240,4309,4403,4459,4542,4609,4693,4781,4843,4907,4970,5037,5134,5240,5331,5433,5492,5551,5628"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "762,3587,3664,3743,3824,3923,4745,4853,4965,5048,8941,9033,9102,13359,13444,13507,13569,13627,13691,13752,13806,13920,13978,14038,14092,14162,14289,14370,14449,14584,14660,14737,14866,14950,15032,15087,15142,15208,15277,15354,15440,15519,15587,15663,15733,15798,15900,15995,16068,16162,16255,16329,16398,16492,16548,16631,16698,16782,16870,16932,16996,17059,17126,17223,17329,17420,17522,17581,17960", "endLines": "22,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,128,83,81,54,54,65,68,76,85,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76", "endOffsets": "920,3659,3738,3819,3918,4007,4848,4960,5043,5107,9028,9097,9156,13439,13502,13564,13622,13686,13747,13801,13915,13973,14033,14087,14157,14284,14365,14444,14579,14655,14732,14861,14945,15027,15082,15137,15203,15272,15349,15435,15514,15582,15658,15728,15793,15895,15990,16063,16157,16250,16324,16393,16487,16543,16626,16693,16777,16865,16927,16991,17054,17121,17218,17324,17415,17517,17576,17635,18032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1028,1125,1230,1316,1416,1529,1607,1684,1775,1868,1962,2056,2156,2249,2344,2438,2529,2620,2699,2809,2912,3008,3119,3221,3331,3490,18037", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "1023,1120,1225,1311,1411,1524,1602,1679,1770,1863,1957,2051,2151,2244,2339,2433,2524,2615,2694,2804,2907,3003,3114,3216,3326,3485,3582,18112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\669eee2c8801e741c32a6ee27b73c23e\\transformed\\core-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "55,56,57,58,59,60,61,219", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4012,4110,4212,4315,4416,4518,4616,18117", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "4105,4207,4310,4411,4513,4611,4740,18213"}}]}]}