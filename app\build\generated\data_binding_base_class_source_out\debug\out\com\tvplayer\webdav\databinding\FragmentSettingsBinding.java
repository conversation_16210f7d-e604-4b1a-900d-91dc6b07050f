// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnAbout;

  @NonNull
  public final Button btnClearCache;

  @NonNull
  public final Button btnMediaScan;

  @NonNull
  public final Button btnPlaybackSettings;

  @NonNull
  public final Button btnWebdavSettings;

  private FragmentSettingsBinding(@NonNull ScrollView rootView, @NonNull Button btnAbout,
      @NonNull Button btnClearCache, @NonNull Button btnMediaScan,
      @NonNull Button btnPlaybackSettings, @NonNull Button btnWebdavSettings) {
    this.rootView = rootView;
    this.btnAbout = btnAbout;
    this.btnClearCache = btnClearCache;
    this.btnMediaScan = btnMediaScan;
    this.btnPlaybackSettings = btnPlaybackSettings;
    this.btnWebdavSettings = btnWebdavSettings;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_about;
      Button btnAbout = ViewBindings.findChildViewById(rootView, id);
      if (btnAbout == null) {
        break missingId;
      }

      id = R.id.btn_clear_cache;
      Button btnClearCache = ViewBindings.findChildViewById(rootView, id);
      if (btnClearCache == null) {
        break missingId;
      }

      id = R.id.btn_media_scan;
      Button btnMediaScan = ViewBindings.findChildViewById(rootView, id);
      if (btnMediaScan == null) {
        break missingId;
      }

      id = R.id.btn_playback_settings;
      Button btnPlaybackSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnPlaybackSettings == null) {
        break missingId;
      }

      id = R.id.btn_webdav_settings;
      Button btnWebdavSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnWebdavSettings == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((ScrollView) rootView, btnAbout, btnClearCache,
          btnMediaScan, btnPlaybackSettings, btnWebdavSettings);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
