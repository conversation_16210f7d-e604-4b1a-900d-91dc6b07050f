<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    android:padding="32dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <!-- 标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="WebDAV服务器配置"
            android:textSize="28sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_marginBottom="32dp" />

        <!-- 服务器名称 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="服务器名称"
            app:boxBackgroundColor="@color/surface_color"
            app:hintTextColor="@color/accent_color">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_server_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 服务器URL -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="服务器URL *"
            app:boxBackgroundColor="@color/surface_color"
            app:hintTextColor="@color/accent_color">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_server_url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:inputType="textUri"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 用户名 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="用户名 *"
            app:boxBackgroundColor="@color/surface_color"
            app:hintTextColor="@color/accent_color">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 密码 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="密码 *"
            app:boxBackgroundColor="@color/surface_color"
            app:hintTextColor="@color/accent_color"
            app:passwordToggleEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:inputType="textPassword"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/btn_test"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="测试连接"
                android:backgroundTint="@color/accent_color"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <Button
                android:id="@+id/btn_connect"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="连接并保存"
                android:backgroundTint="@color/primary_color"
                android:textColor="@color/white"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- 状态显示 -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请输入WebDAV服务器信息"
            android:textSize="16sp"
            android:textColor="@color/accent_color"
            android:textAlignment="center"
            android:gravity="center"
            android:padding="16dp"
            android:background="@drawable/status_background"
            android:layout_marginBottom="16dp" />

        <!-- 说明文字 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="提示：\n• URL格式：https://example.com/webdav\n• 确保服务器支持WebDAV协议\n• 用户需要有读取权限"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:alpha="0.7"
            android:lineSpacingExtra="4dp" />

    </LinearLayout>

</ScrollView>
