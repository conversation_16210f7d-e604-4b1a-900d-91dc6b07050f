<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_webdav_connection" modulePackage="com.tvplayer.webdav" filePath="app\src\main\res\layout\fragment_webdav_connection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_webdav_connection_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="163" endOffset="12"/></Target><Target id="@+id/et_server_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="33" startOffset="12" endLine="40" endOffset="38"/></Target><Target id="@+id/et_server_url" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="53" startOffset="12" endLine="60" endOffset="38"/></Target><Target id="@+id/et_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="73" startOffset="12" endLine="80" endOffset="38"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="94" startOffset="12" endLine="101" endOffset="38"/></Target><Target id="@+id/btn_test" view="Button"><Expressions/><location startLine="113" startOffset="12" endLine="122" endOffset="41"/></Target><Target id="@+id/btn_connect" view="Button"><Expressions/><location startLine="124" startOffset="12" endLine="133" endOffset="41"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="138" startOffset="8" endLine="149" endOffset="48"/></Target></Targets></Layout>