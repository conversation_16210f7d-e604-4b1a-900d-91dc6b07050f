{"logs": [{"outputFile": "com.tvplayer.webdav.app-mergeDebugResources-61:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\127a50fba098f59f5efe01000db9ca9e\\transformed\\material-1.10.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1108,1207,1275,1334,1423,1491,1558,1621,1696,1764,1818,1938,1996,2058,2112,2187,2329,2419,2504,2649,2733,2816,2962,3058,3135,3193,3244,3310,3384,3462,3553,3639,3713,3792,3865,3937,4053,4157,4230,4329,4429,4503,4578,4685,4737,4826,4893,4984,5078,5140,5204,5267,5337,5456,5561,5670,5770,5832,5887", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,79,85,96,89,104,135,84,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,84,144,83,82,145,95,76,57,50,65,73,77,90,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84", "endOffsets": "275,359,439,525,622,712,817,953,1038,1103,1202,1270,1329,1418,1486,1553,1616,1691,1759,1813,1933,1991,2053,2107,2182,2324,2414,2499,2644,2728,2811,2957,3053,3130,3188,3239,3305,3379,3457,3548,3634,3708,3787,3860,3932,4048,4152,4225,4324,4424,4498,4573,4680,4732,4821,4888,4979,5073,5135,5199,5262,5332,5451,5556,5665,5765,5827,5882,5967"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3707,3791,3871,3957,4054,4878,4983,5119,5204,9222,9321,9389,13839,13928,13996,14063,14126,14201,14269,14323,14443,14501,14563,14617,14692,14834,14924,15009,15154,15238,15321,15467,15563,15640,15698,15749,15815,15889,15967,16058,16144,16218,16297,16370,16442,16558,16662,16735,16834,16934,17008,17083,17190,17242,17331,17398,17489,17583,17645,17709,17772,17842,17961,18066,18175,18275,18337,18728", "endLines": "22,50,51,52,53,54,62,63,64,65,118,119,120,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,217", "endColumns": "12,83,79,85,96,89,104,135,84,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,84,144,83,82,145,95,76,57,50,65,73,77,90,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84", "endOffsets": "962,3786,3866,3952,4049,4139,4978,5114,5199,5264,9316,9384,9443,13923,13991,14058,14121,14196,14264,14318,14438,14496,14558,14612,14687,14829,14919,15004,15149,15233,15316,15462,15558,15635,15693,15744,15810,15884,15962,16053,16139,16213,16292,16365,16437,16553,16657,16730,16829,16929,17003,17078,17185,17237,17326,17393,17484,17578,17640,17704,17767,17837,17956,18061,18170,18270,18332,18387,18808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ea86b6b5024288572ab34cbf40f675ee\\transformed\\preference-1.0.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "105,192,361,446", "endColumns": "86,168,84,80", "endOffsets": "187,356,441,522"}, "to": {"startLines": "117,220,221,222", "startColumns": "4,4,4,4", "startOffsets": "9135,18999,19168,19253", "endColumns": "86,168,84,80", "endOffsets": "9217,19163,19248,19329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b49b0b48b16cd290d5f8e8dae47d19e\\transformed\\leanback-1.0.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,508,646,763,862,956,1093,1228,1335,1463,1616,1748,1878,1995,2088,2221,2312,2415,2521,2626,2721,2838,2960,3081,3200,3310,3425,3552,3672,3795,3911,3998,4084,4193,4333,4496", "endColumns": "106,100,99,94,137,116,98,93,136,134,106,127,152,131,129,116,92,132,90,102,105,104,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,162,98", "endOffsets": "207,308,408,503,641,758,857,951,1088,1223,1330,1458,1611,1743,1873,1990,2083,2216,2307,2410,2516,2621,2716,2833,2955,3076,3195,3305,3420,3547,3667,3790,3906,3993,4079,4188,4328,4491,4590"}, "to": {"startLines": "121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9448,9555,9656,9756,9851,9989,10106,10205,10299,10436,10571,10678,10806,10959,11091,11221,11338,11431,11564,11655,11758,11864,11969,12064,12181,12303,12424,12543,12653,12768,12895,13015,13138,13254,13341,13427,13536,13676,18629", "endColumns": "106,100,99,94,137,116,98,93,136,134,106,127,152,131,129,116,92,132,90,102,105,104,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,162,98", "endOffsets": "9550,9651,9751,9846,9984,10101,10200,10294,10431,10566,10673,10801,10954,11086,11216,11333,11426,11559,11650,11753,11859,11964,12059,12176,12298,12419,12538,12648,12763,12890,13010,13133,13249,13336,13422,13531,13671,13834,18723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d7a76f9f06e832d74a32eccee18e4a53\\transformed\\exoplayer-ui-2.19.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1861,1981,2101,2170,2257,2331,2411,2502,2593,2658,2722,2775,2833,2881,2942,3007,3069,3134,3202,3266,3324,3390,3454,3520,3572,3634,3713,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1856,1976,2096,2165,2252,2326,2406,2497,2588,2653,2717,2770,2828,2876,2937,3002,3064,3129,3197,3261,3319,3385,3449,3515,3567,3629,3708,3787,3844"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,592,5269,5360,5451,5531,5616,5707,5785,5851,5952,6055,6122,6187,6249,6320,6438,6558,6678,6747,6834,6908,6988,7079,7170,7235,8008,8061,8119,8167,8228,8293,8355,8420,8488,8552,8610,8676,8740,8806,8858,8920,8999,9078", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "375,587,782,5355,5446,5526,5611,5702,5780,5846,5947,6050,6117,6182,6244,6315,6433,6553,6673,6742,6829,6903,6983,7074,7165,7230,7294,8056,8114,8162,8223,8288,8350,8415,8483,8547,8605,8671,8735,8801,8853,8915,8994,9073,9130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7243958f9f6ec3b8adc410641a8629a1\\transformed\\exoplayer-core-2.19.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7299,7376,7440,7508,7574,7654,7732,7832,7930", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "7371,7435,7503,7569,7649,7727,7827,7925,8003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4f28b47242b8502d63ceee6844d2d99\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "967,1078,1186,1299,1387,1493,1608,1688,1765,1856,1949,2044,2138,2238,2331,2426,2520,2611,2702,2786,2895,3005,3106,3216,3334,3442,3605,18813", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "1073,1181,1294,1382,1488,1603,1683,1760,1851,1944,2039,2133,2233,2326,2421,2515,2606,2697,2781,2890,3000,3101,3211,3329,3437,3600,3702,18893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\669eee2c8801e741c32a6ee27b73c23e\\transformed\\core-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "55,56,57,58,59,60,61,219", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4144,4241,4343,4444,4541,4648,4756,18898", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "4236,4338,4439,4536,4643,4751,4873,18994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae9e4a68b83caa9178cf3717646936e\\transformed\\navigation-ui-2.7.5\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "214,215", "startColumns": "4,4", "startOffsets": "18392,18506", "endColumns": "113,122", "endOffsets": "18501,18624"}}]}]}