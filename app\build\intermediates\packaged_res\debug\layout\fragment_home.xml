<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 动态背景：聚焦项目的 backdrop 图 -->
    <ImageView
        android:id="@+id/iv_backdrop"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:alpha="0.0" />

    <!-- 顶部到内容的渐变遮罩，保证文字可读性 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/backdrop_gradient" />



    <!-- 内容层 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="32dp"
            android:paddingEnd="32dp"
            android:paddingTop="16dp"
            android:paddingBottom="32dp">

            <!-- 现代化顶部导航栏 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingVertical="12dp"
                android:layout_marginBottom="24dp">

                <!-- 左侧Logo和标题 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_play"
                        android:tint="@color/accent_color"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="TV Player"
                        android:textSize="20sp"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>



            </LinearLayout>

            <!-- 分类导航标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="分类"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_all_categories"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="全部"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:focusable="true"
                    android:clickable="true"
                    android:background="@drawable/text_button_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_categories"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="48dp"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="32dp" />

            <!-- 继续观看标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="继续观看"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_all_continue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="全部"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:focusable="true"
                    android:clickable="true"
                    android:background="@drawable/text_button_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_continue_watching"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="24dp" />

            <!-- 最近观看标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="最近观看"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_all_recent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="全部 6 >"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:focusable="true"
                    android:clickable="true"
                    android:background="@drawable/text_button_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_recently_added"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="24dp" />

            <!-- 电影标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="电影"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />



            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_movies"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="40dp"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="32dp"
                android:nestedScrollingEnabled="false" />

            <!-- 电视剧标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="电视剧"
                    android:textSize="18sp"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />



            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_tv_shows"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:paddingStart="0dp"
                android:paddingEnd="32dp"
                android:nestedScrollingEnabled="false"
                android:layout_marginBottom="32dp" />

        </LinearLayout>
    </ScrollView>

</FrameLayout>
