// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ActivityVideoDetailsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final FrameLayout detailsFragmentContainer;

  private ActivityVideoDetailsBinding(@NonNull FrameLayout rootView,
      @NonNull FrameLayout detailsFragmentContainer) {
    this.rootView = rootView;
    this.detailsFragmentContainer = detailsFragmentContainer;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVideoDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVideoDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_video_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVideoDetailsBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    FrameLayout detailsFragmentContainer = (FrameLayout) rootView;

    return new ActivityVideoDetailsBinding((FrameLayout) rootView, detailsFragmentContainer);
  }
}
