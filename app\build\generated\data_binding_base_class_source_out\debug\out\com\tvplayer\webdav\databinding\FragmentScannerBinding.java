// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentScannerBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnPickMoviesDir;

  @NonNull
  public final Button btnPickTvDir;

  @NonNull
  public final Button btnScanMovies;

  @NonNull
  public final Button btnScanTv;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvMoviesDir;

  @NonNull
  public final TextView tvScanProgress;

  @NonNull
  public final TextView tvScanStatus;

  @NonNull
  public final TextView tvTvDir;

  private FragmentScannerBinding(@NonNull ScrollView rootView, @NonNull Button btnPickMoviesDir,
      @NonNull Button btnPickTvDir, @NonNull Button btnScanMovies, @NonNull Button btnScanTv,
      @NonNull ProgressBar progressBar, @NonNull TextView tvMoviesDir,
      @NonNull TextView tvScanProgress, @NonNull TextView tvScanStatus, @NonNull TextView tvTvDir) {
    this.rootView = rootView;
    this.btnPickMoviesDir = btnPickMoviesDir;
    this.btnPickTvDir = btnPickTvDir;
    this.btnScanMovies = btnScanMovies;
    this.btnScanTv = btnScanTv;
    this.progressBar = progressBar;
    this.tvMoviesDir = tvMoviesDir;
    this.tvScanProgress = tvScanProgress;
    this.tvScanStatus = tvScanStatus;
    this.tvTvDir = tvTvDir;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentScannerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentScannerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_scanner, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentScannerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_pick_movies_dir;
      Button btnPickMoviesDir = ViewBindings.findChildViewById(rootView, id);
      if (btnPickMoviesDir == null) {
        break missingId;
      }

      id = R.id.btn_pick_tv_dir;
      Button btnPickTvDir = ViewBindings.findChildViewById(rootView, id);
      if (btnPickTvDir == null) {
        break missingId;
      }

      id = R.id.btn_scan_movies;
      Button btnScanMovies = ViewBindings.findChildViewById(rootView, id);
      if (btnScanMovies == null) {
        break missingId;
      }

      id = R.id.btn_scan_tv;
      Button btnScanTv = ViewBindings.findChildViewById(rootView, id);
      if (btnScanTv == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_movies_dir;
      TextView tvMoviesDir = ViewBindings.findChildViewById(rootView, id);
      if (tvMoviesDir == null) {
        break missingId;
      }

      id = R.id.tv_scan_progress;
      TextView tvScanProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvScanProgress == null) {
        break missingId;
      }

      id = R.id.tv_scan_status;
      TextView tvScanStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvScanStatus == null) {
        break missingId;
      }

      id = R.id.tv_tv_dir;
      TextView tvTvDir = ViewBindings.findChildViewById(rootView, id);
      if (tvTvDir == null) {
        break missingId;
      }

      return new FragmentScannerBinding((ScrollView) rootView, btnPickMoviesDir, btnPickTvDir,
          btnScanMovies, btnScanTv, progressBar, tvMoviesDir, tvScanProgress, tvScanStatus,
          tvTvDir);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
