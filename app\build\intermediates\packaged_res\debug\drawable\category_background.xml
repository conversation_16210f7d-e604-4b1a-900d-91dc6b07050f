<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 获得焦点时 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background_focused" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/accent_color" />
        </shape>
    </item>

    <!-- 按下时 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/card_background_pressed" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/primary_color" />
        </shape>
    </item>

    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/surface_color" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/card_border" />
        </shape>
    </item>

</selector>
