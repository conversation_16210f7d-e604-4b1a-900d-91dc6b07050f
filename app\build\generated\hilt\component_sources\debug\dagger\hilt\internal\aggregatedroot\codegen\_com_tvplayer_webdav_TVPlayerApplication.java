package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.tvplayer.webdav.TVPlayerApplication",
    rootPackage = "com.tvplayer.webdav",
    originatingRoot = "com.tvplayer.webdav.TVPlayerApplication",
    originatingRootPackage = "com.tvplayer.webdav",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "TVPlayerApplication",
    originatingRootSimpleNames = "TVPlayerApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_tvplayer_webdav_TVPlayerApplication {
}
